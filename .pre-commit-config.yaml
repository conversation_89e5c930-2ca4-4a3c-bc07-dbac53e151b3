# See https://pre-commit.com for more information

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.1.0
    hooks:
      # https://pre-commit.com/hooks.html
      - id: check-merge-conflict
      - id: trailing-whitespace
        exclude: .github/workflows
      - id: end-of-file-fixer
        exclude: .github/badges
      - id: check-yaml
        exclude: helm
      - id: check-json
      - id: check-toml
      - id: check-added-large-files
      - id: detect-private-key
      - id: detect-aws-credentials
        args:
          - --allow-missing-credentials
  - repo: local
    hooks:
      - id: black
        name: Black Code Formatter
        language: system
        entry: poetry run black
        types: [python]
        require_serial: true
        exclude: docs/
