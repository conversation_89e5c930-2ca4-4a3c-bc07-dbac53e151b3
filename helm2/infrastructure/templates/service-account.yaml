apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.application.name }}-svc-account-{{ .Values.environment }}
  labels:
{{ include "mychart.common_labels" .  | indent 4 }}
  annotations:
    eks.amazonaws.com/role-arn: {{ include "service-account.role" . }}
    app.tr.com/deployment-specification: "https://github.com/tr/ras-search_ai-conversations/blob/main/helm2/infrastructure/templates/service-account.yaml"
{{ include "mychart.resource_annotations" . | indent 4 }}
