{{/*
Common labels
*/}}
{{- define "mychart.common_labels" -}}
app: {{ .Values.application.name }}
env: {{ .Values.environment }}
{{- end }}

{{/*
Resource Annotations
*/}}
{{- define "mychart.resource_annotations" -}}
app.tr.com/application-asset-insight-id: "207891"
app.tr.com/resource-author: "<EMAIL>"
app.tr.com/resource-owner: "<EMAIL>"
app.kubernetes.io/managed-by: "helm"
app.tr.com/vendor: "Thomson Reuters"
app.tr.com/repo: "https://github.com/tr/ras-search_ai-conversations"
{{- end -}}

{{/*
Returns right privateHostedZoneName from values file based on environment
*/}}
{{- define "gateway.privateHostedZoneName" -}}
{{- if eq .Values.environment "ci" -}}
{{- .Values.gateway.PreProdPrivatehostedZoneName -}}
{{- else if eq .Values.environment "dev" -}}
{{- .Values.gateway.PreProdPrivatehostedZoneName -}}
{{- else if eq .Values.environment "int" -}}
{{- .Values.gateway.PreProdPrivatehostedZoneName -}}
{{- else if eq .Values.environment "qa" -}}
{{- .Values.gateway.PreProdPrivatehostedZoneName -}}
{{- else if eq .Values.environment "prod" -}}
{{- .Values.gateway.ProdPrivatehostedZoneName -}}
{{- else -}}
{{- printf "unsupported-private-hosted-zone-name-%s" .Values.environment -}}
{{- end -}}
{{- end -}}

{{/*
Returns right route53 privateHostedZoneName from values file based on environment
*/}}
{{- define "gateway.route53.privateHostedZoneName" -}}
{{- if (eq .Values.environment "ci") -}}
{{- .Values.gateway.route53.PreProdPrivateHostedZoneName -}}
{{- else if (eq .Values.environment "dev") -}}
{{- .Values.gateway.route53.PreProdPrivateHostedZoneName -}}
{{- else if (eq .Values.environment "int") -}}
{{- .Values.gateway.route53.PreProdPrivateHostedZoneName -}}
{{- else if (eq .Values.environment "qa") -}}
{{- .Values.gateway.route53.PreProdPrivateHostedZoneName -}}
{{- else if (eq .Values.environment "prod") -}}
{{- .Values.gateway.route53.ProdPrivateHostedZoneName -}}
{{- else -}}
{{- printf "unsupported-private-hosted-zone-name-%s" .Values.environment -}}
{{- end -}}
{{- end -}}

{{/*
Returns right service-account role from values file based on environment
*/}}
{{- define "service-account.role" -}}
{{- if eq .Values.environment "ci" -}}
{{- .Values.service.ci.IamRole -}}
{{- else if eq .Values.environment "dev" -}}
{{- .Values.service.ci.IamRole -}}
{{- else if eq .Values.environment "int" -}}
{{- .Values.service.int.IamRole -}}
{{- else if eq .Values.environment "qa" -}}
{{- .Values.service.qa.IamRole -}}
{{- else if eq .Values.environment "prod" -}}
{{- .Values.service.prod.IamRole -}}
{{- else -}}
{{- printf "unsupported-service-environment-%s" .Values.environment -}}
{{- end -}}
{{- end -}}

{{/*
Returns right sectigo cert secret name from values file based on environment
*/}}
{{- define "gateway.tls.credentialName.sectigoSecretName" -}}
{{- if (eq .Values.environment "ci") -}}
{{- .Values.gateway.tls.credentialName.PreProdSectigoSecretName -}}
{{- else if (eq .Values.environment "dev") -}}
{{- .Values.gateway.tls.credentialName.PreProdSectigoSecretName -}}
{{- else if (eq .Values.environment "int") -}}
{{- .Values.gateway.tls.credentialName.PreProdSectigoSecretName -}}
{{- else if (eq .Values.environment "qa") -}}
{{- .Values.gateway.tls.credentialName.PreProdSectigoSecretName -}}
{{- else if (eq .Values.environment "prod") -}}
{{- .Values.gateway.tls.credentialName.ProdSectigoSecretName -}}
{{- else -}}
{{- printf "unsupported-sectigo-secret-name-%s" .Values.environment -}}
{{- end -}}
{{- end -}}