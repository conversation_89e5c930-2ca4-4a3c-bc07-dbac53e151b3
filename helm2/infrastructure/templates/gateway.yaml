apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ .Chart.Name }}-gateway-{{ .Values.environment }}
  labels:
{{ include "mychart.common_labels" .  | indent 4 }}
  annotations:
    app.tr.com/deployment-specification: "https://github.com/tr/ras-search_ai-conversations/blob/main/helm2/infrastructure/templates/gateway.yaml"
{{ include "mychart.resource_annotations" . | indent 4 }}
spec:
  selector:
    istio: ingressgateway # use istio default controller
  servers:
    - port:
        name:  https-private
        number: {{ .Values.gateway.https }}
        protocol:  HTTPS
      hosts:
        - {{ .Values.application.name }}-{{ .Values.environment }}.{{ include "gateway.privateHostedZoneName" . }}
      tls:
        credentialName: {{ include "gateway.tls.credentialName.sectigoSecretName" . }}
        mode: {{ .Values.gateway.tls.mode }}
    - port:
        name: https-private-route53
        number: {{ .Values.gateway.https }}
        protocol: HTTPS
      hosts:
        - {{ .Values.application.name }}-{{ .Values.environment }}.{{ include "gateway.route53.privateHostedZoneName" . }}
      tls:
        credentialName: {{ include "gateway.tls.credentialName.sectigoSecretName" . }}
        mode: {{ .Values.gateway.tls.mode }}