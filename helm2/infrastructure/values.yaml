application:
  name: ai-conversations

gateway:
  route53:
    PreProdPrivateHostedZoneName: 4649.aws-int.thomsonreuters.com
    ProdPrivateHostedZoneName: 78677.aws-int.thomsonreuters.com
  PreProdPrivatehostedZoneName: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ProdPrivatehostedZoneName: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  https: 443
  tls :
    credentialName:
      PreProdSectigoSecretName: a207891-ai-conversations-preprod-plexus-secret
      ProdSectigoSecretName: a207891-ai-conversations-prod-plexus-secret
    mode: SIMPLE

service:
  prod:
    IamRole: arn:aws:iam::935712178677:role/a207891-ai-conversations-prod-use1-eks
  int:
    IamRole: arn:aws:iam::653661534649:role/a207891-ai-conversations-int-use1-eks
  ci:
    IamRole: arn:aws:iam::653661534649:role/a207891-ai-conversations-ci-use1-eks
  qa:
    IamRole: arn:aws:iam::653661534649:role/a207891-ai-conversations-qa-use1-eks
  containerPort : 8000
  servicePort: 80
