name: ai-conversations

apiVersion: v2

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.1.1



# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# The command `sed` is used to modify this value during bake stage.
appVersion: HASH  # This value is filled in by the bakespec.yaml script during the bake stage
description: |
  ai-conversations microservice deployment into Plexus
type: application
