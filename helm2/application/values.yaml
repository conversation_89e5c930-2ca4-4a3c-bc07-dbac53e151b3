deployment :
  replicaCount:
    dev: 1
    ci: 2
    int: 6
    qa: 40
    prod: 40
  terminationDrainDuration: 300s
  terminationGracePeriodSeconds: 300
  image:
    containerPort : 8000
    pullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 3
        memory: 4Gi
      requests:
        cpu: 1500m
        memory: 2Gi
    livenessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      timeoutSeconds: 10
      periodSeconds: 30
      failureThreshold: 5
    startupProbe:
      periodSeconds: 10
      failureThreshold: 10
    readinessProbe:
      httpGet:
        port: 8000
      initialDelaySeconds: 40
      periodSeconds: 20
hpa:
  max:
    dev: 1
    ci: 6
    int: 40
    qa: 40
    prod: 40
  cpu:
    targetAverageUtilization: 60
  memory:
    targetAverageUtilization: 65
poddb:
  maxUnavailable: "20%"

virtualservice :
  port:
    number: 80
  weight: 100

destination:
  subset: app-deployment
