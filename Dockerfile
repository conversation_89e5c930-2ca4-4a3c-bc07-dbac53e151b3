ARG REGION_LONG
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11-dev AS build
# Operations as root user
USER root
# python 3.9 is already installed on aws linux 2. The tr golden image doesn't remove it.
# We need to install openssl, tar and gzip to install helm
RUN apk add openssl && apk add gzip && apk add curl
RUN /usr/bin/python3.11 -m ensurepip --upgrade
RUN curl -O https://bootstrap.pypa.io/get-pip.py && python3 get-pip.py && rm get-pip.py
# Download and install helm
COPY /scripts /scripts
RUN chmod 700 /scripts/get_helm.sh
RUN /scripts/get_helm.sh
RUN rm -rf /scripts

RUN mkdir -p /home/<USER>/home/<USER>

# Operations as nonroot user
USER 65532:65532
ENV HOME="/home/<USER>"
WORKDIR /home/<USER>

COPY --chown=65532:65532 requirements.txt .

ARG PIP_EXTRA_INDEX_URL
ARG ARTIFACTORY_USER
ARG ARTIFACTORY_TOKEN
ENV PIP_EXTRA_INDEX_URL=https://${ARTIFACTORY_USER}:${ARTIFACTORY_TOKEN}@tr1.jfrog.io/tr1/api/pypi/pypi/simple
# Install dependencies
RUN python -m venv /home/<USER>/venv
ENV PATH="/home/<USER>/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt && pip cache purge

# Copy application code
COPY --chown=65532:65532 /app /home/<USER>/app

# Final stage
FROM 460300312212.dkr.ecr.${REGION_LONG}.amazonaws.com/tr-chainguard/python-fips:3.11

# Add Maintainer Info and Labels
LABEL maintainer="<EMAIL>"
LABEL com.tr.application-asset-insight-id="207891"
LABEL org.opencontainers.image.authors="<EMAIL>"
LABEL com.tr.service-contact="<EMAIL>"
LABEL org.opencontainers.image.source="https://github.com/tr/ras-search_ai-conversations/blob/main/Dockerfile"
LABEL org.opencontainers.image.version="APPVERSION"
LABEL org.opencontainers.image.vendor="Thomson Reuters"
LABEL org.opencontainers.image.url="https://github.com/tr/ras-search_ai-conversations"

USER 65532:65532
ENV HOME="/home/<USER>"
WORKDIR /home/<USER>

COPY --from=build --chown=65532:65532 /home/<USER>/home/<USER>
COPY --from=build --chown=65532:65532 /usr/local/bin/helm /usr/local/bin/helm

ENV PATH="/home/<USER>/venv/bin:/usr/local/bin:$PATH"
ENV PYTHONPATH="/home/<USER>/app:$PYTHONPATH"

# Set TMPDIR to the custom temp directory we created
ENV TMPDIR="/home/<USER>/tmp"

# Enable FIPS endpoints for all AWS services
ENV AWS_USE_FIPS_ENDPOINT=true

EXPOSE 8000

ENTRYPOINT ["gunicorn", "main:app",\
    "--timeout", "600",\
    "--workers", "4",\
    "--threads", "2",\
    "--worker-class", "uvicorn.workers.UvicornWorker",\
    "--bind", "0.0.0.0:8000",\
    "--logger-class", "app.utils.gunicorn_logger.GunicornLogger"]

