## v0.15.16 (2025-04-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.15.16)

### What's Changed

* Update CHANGELOG.md file for QA version v0.15.11 by @BhupeshParakhTR in [#444](https://github.com/tr/ras-search_ai-conversations/pull/444)
* reverting the poetry changes by @BhupeshParakhTR in [#445](https://github.com/tr/ras-search_ai-conversations/pull/445)
* Integrated new intent annotations for PL Employee policy by @BhupeshParakhTR in [#446](https://github.com/tr/ras-search_ai-conversations/pull/446)
* Reverting changes that were done for PL Employee Policy demo by @TylerJacksonTR in [#447](https://github.com/tr/ras-search_ai-conversations/pull/447)
* Increasing QA/PROD Pod Counts by @TylerJacksonTR in [#449](https://github.com/tr/ras-search_ai-conversations/pull/449)


## v0.15.11 (2025-03-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.15.11)

### What's Changed

* Update CHANGELOG.md file for QA version v0.15.9 by @BhupeshParakhTR in [#440](https://github.com/tr/ras-search_ai-conversations/pull/440)
* Update CHANGELOG.md file for QA version v0.15.10 by @BhupeshParakhTR in [#442](https://github.com/tr/ras-search_ai-conversations/pull/442)
* Fixing v2 to v3 translation for intent conversation entries by @TylerJacksonTR in [#443](https://github.com/tr/ras-search_ai-conversations/pull/443)


## v0.15.10 (2025-03-18)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.15.10)

### What's Changed

* AB#2116863 - Adding support for passing tr-passthru headers under UserSession data by @TylerJacksonTR in [#439](https://github.com/tr/ras-search_ai-conversations/pull/439)


## v0.15.9 (2025-03-18)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.15.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.15.8 by @BhupeshParakhTR in [#437](https://github.com/tr/ras-search_ai-conversations/pull/437)


## v0.15.8 (2025-03-12)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.15.8)

### What's Changed

* Revert "RAS_CONFIG_BASE_URL has been pointed to QA" by @BhupeshParakhTR in [#411](https://github.com/tr/ras-search_ai-conversations/pull/411)
* Update CHANGELOG.md file for QA version v0.14.11 by @BhupeshParakhTR in [#412](https://github.com/tr/ras-search_ai-conversations/pull/412)
* AB#2090316 Updated docker file to use chainguard fips image by @AmitChoudharyTR in [#413](https://github.com/tr/ras-search_ai-conversations/pull/413)
* Ab#2092097 by @jaredbroekhuisTR in [#415](https://github.com/tr/ras-search_ai-conversations/pull/415)
* New shared utils to fix cache issue by @JoshGrittnerTR in [#418](https://github.com/tr/ras-search_ai-conversations/pull/418)
* AB#2107522 - Conversation Snapshots V3 updates by @RatnakarTR in [#417](https://github.com/tr/ras-search_ai-conversations/pull/417)
* BUG#2112839 - Conversation Snapshot API  500 Error Bug Fix by @RatnakarTR in [#420](https://github.com/tr/ras-search_ai-conversations/pull/420)
* AB#2113087 - Fixing the header issue in PROD for PL US by @BhupeshParakhTR in [#419](https://github.com/tr/ras-search_ai-conversations/pull/419)
* updating validations for headers, removing search for 'none' by @jaredbroekhuisTR in [#421](https://github.com/tr/ras-search_ai-conversations/pull/421)
* Updating RAS Config to QA by @akshathaaithalrTR in [#422](https://github.com/tr/ras-search_ai-conversations/pull/422)
* Update the request evaluator profile to have the same suffixes as the… by @JoshGrittnerTR in [#423](https://github.com/tr/ras-search_ai-conversations/pull/423)
* AB#2114835- Adding the profile fields in conversation list API  by @akshathaaithalrTR in [#424](https://github.com/tr/ras-search_ai-conversations/pull/424)
* AB#2121341 Updating the RAS_CONFIG_URL to QA by @akshathaaithalrTR in [#425](https://github.com/tr/ras-search_ai-conversations/pull/425)
* AB#2120480 - Conversations Intermittently in Failed Status After POST by @TylerJacksonTR in [#426](https://github.com/tr/ras-search_ai-conversations/pull/426)
* AB#2111031 - Support for PL Request Evaluator by @TylerJacksonTR in [#427](https://github.com/tr/ras-search_ai-conversations/pull/427)
* pinned url lib at last 1.26.x version. Updated shared utils for guide… by @scottberres-tr in [#431](https://github.com/tr/ras-search_ai-conversations/pull/431)
* Upgrade Python utils for custom metadata by @PawanBapatTR in [#433](https://github.com/tr/ras-search_ai-conversations/pull/433)
* Profile blocks and nudges by @JoshGrittnerTR in [#435](https://github.com/tr/ras-search_ai-conversations/pull/435)
* debug info enabled if present by @scottberres-tr in [#436](https://github.com/tr/ras-search_ai-conversations/pull/436)

### New Contributors

* @PawanBapatTR made their first contribution in [#433](https://github.com/tr/ras-search_ai-conversations/pull/433)


## v0.14.11 (2024-12-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.11)

### What's Changed

* Update CHANGELOG.md file for QA version v0.14.10 by @BhupeshParakhTR in [#406](https://github.com/tr/ras-search_ai-conversations/pull/406)
* RAS_CONFIG_BASE_URL has been pointed to QA by @AritraTR in [#408](https://github.com/tr/ras-search_ai-conversations/pull/408)

### New Contributors

* @AritraTR made their first contribution in [#408](https://github.com/tr/ras-search_ai-conversations/pull/408)


## v0.14.10 (2024-12-19)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.10)

### What's Changed

* Update CHANGELOG.md file for QA version v0.14.9 by @BhupeshParakhTR in [#405](https://github.com/tr/ras-search_ai-conversations/pull/405)
* v2 Snapshot updates by @RatnakarTR in [#402](https://github.com/tr/ras-search_ai-conversations/pull/402)


## v0.14.9 (2024-12-19)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.14.8 by @BhupeshParakhTR in [#401](https://github.com/tr/ras-search_ai-conversations/pull/401)
* Ab#2091467 by @jaredbroekhuisTR in [#404](https://github.com/tr/ras-search_ai-conversations/pull/404)


## v0.14.8 (2024-12-17)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.8)

### What's Changed

* Update CHANGELOG.md file for QA version v0.14.7 by @BhupeshParakhTR in [#397](https://github.com/tr/ras-search_ai-conversations/pull/397)
* updates to add validation for incoming headers to resolve QA findings by @jaredbroekhuisTR in [#400](https://github.com/tr/ras-search_ai-conversations/pull/400)


## v0.14.7 (2024-12-13)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.7)

### What's Changed

* Poetry update for modified Snapshot Utils by @RatnakarTR in [#393](https://github.com/tr/ras-search_ai-conversations/pull/393)
* Update CHANGELOG.md file for QA version v0.14.5 by @BhupeshParakhTR in [#394](https://github.com/tr/ras-search_ai-conversations/pull/394)
* getting helm files updated for route 53 records by @AdamPetersonTR in [#396](https://github.com/tr/ras-search_ai-conversations/pull/396)


## v0.14.5 (2024-12-12)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.14.5)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.29 by @BhupeshParakhTR in [#380](https://github.com/tr/ras-search_ai-conversations/pull/380)
* calling v2 method to get all queues at once by @scottberres-tr in [#382](https://github.com/tr/ras-search_ai-conversations/pull/382)
* AB#2091180 - is_favorite field is updated in followup now by @BhupeshParakhTR in [#383](https://github.com/tr/ras-search_ai-conversations/pull/383)
* AB#2089847 - Conversation Snapshot Endpoint by @RatnakarTR in [#381](https://github.com/tr/ras-search_ai-conversations/pull/381)
* Revert "AB#2089847 - Conversation Snapshot Endpoint" by @RatnakarTR in [#385](https://github.com/tr/ras-search_ai-conversations/pull/385)
* AB#2089847 - Conversation Snapshot Endpoint by @RatnakarTR in [#386](https://github.com/tr/ras-search_ai-conversations/pull/386)
* AB#2089847 - Conversation Snapshot Endpoint by @RatnakarTR in [#387](https://github.com/tr/ras-search_ai-conversations/pull/387)
* AB#2089847 - Conversation Snapshot Tests by @RatnakarTR in [#388](https://github.com/tr/ras-search_ai-conversations/pull/388)
* Create Release for Snapshot Router(Pipeline didnt trigger for the previous release) by @RatnakarTR in [#389](https://github.com/tr/ras-search_ai-conversations/pull/389)
* Exception handling for Snapshot Endpoints by @RatnakarTR in [#391](https://github.com/tr/ras-search_ai-conversations/pull/391)


## v0.13.29 (2024-12-04)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.29)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.28 by @BhupeshParakhTR in [#377](https://github.com/tr/ras-search_ai-conversations/pull/377)
* AB#2095426 - AI Conversations - In Progress Conversation Validation Fixes by @TylerJacksonTR in [#378](https://github.com/tr/ras-search_ai-conversations/pull/378)


## v0.13.28 (2024-11-22)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.28)

### What's Changed

* V3 addtl user inputs extra by @TylerJacksonTR in [#370](https://github.com/tr/ras-search_ai-conversations/pull/370)
* Update CHANGELOG.md file for QA version v0.13.27 by @BhupeshParakhTR in [#374](https://github.com/tr/ras-search_ai-conversations/pull/374)
* AB#2092283 - V3 - Support for Additional User Inputs by @TylerJacksonTR in [#375](https://github.com/tr/ras-search_ai-conversations/pull/375)


## v0.13.27 (2024-11-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.27)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.26 by @BhupeshParakhTR in [#369](https://github.com/tr/ras-search_ai-conversations/pull/369)
* thread pool for request evaluator calls non-blocking by @JoshGrittnerTR in [#372](https://github.com/tr/ras-search_ai-conversations/pull/372)


## v0.13.26 (2024-11-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.26)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.25 by @BhupeshParakhTR in [#367](https://github.com/tr/ras-search_ai-conversations/pull/367)
* AB#2094023 Handles if user input empty by @Nirmalkumar3 in [#368](https://github.com/tr/ras-search_ai-conversations/pull/368)

### New Contributors

* @Nirmalkumar3 made their first contribution in [#368](https://github.com/tr/ras-search_ai-conversations/pull/368)


## v0.13.25 (2024-11-19)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.25)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.23 by @BhupeshParakhTR in [#364](https://github.com/tr/ras-search_ai-conversations/pull/364)
* AB#2092414 Fixed snyk issues by @AmitChoudharyTR in [#365](https://github.com/tr/ras-search_ai-conversations/pull/365)
* Remove additional 433 error by @JoshGrittnerTR in [#366](https://github.com/tr/ras-search_ai-conversations/pull/366)


## v0.13.23 (2024-11-18)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.23)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.21 by @BhupeshParakhTR in [#360](https://github.com/tr/ras-search_ai-conversations/pull/360)
* Update CHANGELOG.md file for QA version v0.13.22 by @BhupeshParakhTR in [#361](https://github.com/tr/ras-search_ai-conversations/pull/361)
* Ab#2094697 - v3 Conversation Type does not have the values Initial or FollowUp by @TylerJacksonTR in [#362](https://github.com/tr/ras-search_ai-conversations/pull/362)


## v0.13.22 (2024-11-15)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.22)

### What's Changed

* updating shared utils version by @jaredbroekhuisTR in [#359](https://github.com/tr/ras-search_ai-conversations/pull/359)

### New Contributors

* @jaredbroekhuisTR made their first contribution in [#359](https://github.com/tr/ras-search_ai-conversations/pull/359)


## v0.13.21 (2024-11-15)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.21)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.19 by @BhupeshParakhTR in [#357](https://github.com/tr/ras-search_ai-conversations/pull/357)
* AB#2093458: Add Asset ID header by @JoshGrittnerTR in [#356](https://github.com/tr/ras-search_ai-conversations/pull/356)
* V3 email fix for when requests have it in the initial request (useful… by @TylerJacksonTR in [#358](https://github.com/tr/ras-search_ai-conversations/pull/358)


## v0.13.19 (2024-11-13)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.19)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.18 by @BhupeshParakhTR in [#354](https://github.com/tr/ras-search_ai-conversations/pull/354)
* AB#2086024 - Quick Follow Up Conversation Issues by @TylerJacksonTR in [#355](https://github.com/tr/ras-search_ai-conversations/pull/355)


## v0.13.18 (2024-11-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.18)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.17 by @BhupeshParakhTR in [#352](https://github.com/tr/ras-search_ai-conversations/pull/352)
* AB#2091094 - Missing fields from V3 GET request on conversation started in V2 by @TylerJacksonTR in [#353](https://github.com/tr/ras-search_ai-conversations/pull/353)


## v0.13.17 (2024-11-07)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.17)

### What's Changed

* AB#2085983- Response is coming in skill router service even when no value is given under subscribed skills by @BhupeshParakhTR in [#345](https://github.com/tr/ras-search_ai-conversations/pull/345)
* Update CHANGELOG.md file for QA version v0.13.12 by @BhupeshParakhTR in [#346](https://github.com/tr/ras-search_ai-conversations/pull/346)
* AB#2086696 V3 Testing – Input Validation service is not showing any error when answer_solution_profile is kept blank by @akshathaaithalrTR in [#342](https://github.com/tr/ras-search_ai-conversations/pull/342)
* Revert "AB#2086696 V3 Testing – Input Validation service is not showing any error when answer_solution_profile is kept blank" by @akshathaaithalrTR in [#348](https://github.com/tr/ras-search_ai-conversations/pull/348)
* AB#2086696 V3 Testing – Input Validation service is not showing any error when answer_solution_profile is kept blank by @akshathaaithalrTR in [#349](https://github.com/tr/ras-search_ai-conversations/pull/349)
* AB#2090686 - QA - V2 testing - Data retention is always 365_days by @TylerJacksonTR in [#350](https://github.com/tr/ras-search_ai-conversations/pull/350)


## v0.13.12 (2024-11-04)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.12)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.10 by @BhupeshParakhTR in [#340](https://github.com/tr/ras-search_ai-conversations/pull/340)
* Update CHANGELOG.md file for QA version v0.13.11 by @BhupeshParakhTR in [#341](https://github.com/tr/ras-search_ai-conversations/pull/341)
* AB#2090676 - 500 Error when Retrieving V2 Created Conversation with V3 GET Request by @TylerJacksonTR in [#344](https://github.com/tr/ras-search_ai-conversations/pull/344)


## v0.13.11 (2024-10-30)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.11)

### What's Changed

* AB#2089734 - V3 Testing - PUT on Nudged Skill Router Does Not Continue At Intent by @TylerJacksonTR in [#339](https://github.com/tr/ras-search_ai-conversations/pull/339)


## v0.13.10 (2024-10-30)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.10)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.5 by @BhupeshParakhTR in [#334](https://github.com/tr/ras-search_ai-conversations/pull/334)
* Update CHANGELOG.md file for QA version v0.13.6 by @BhupeshParakhTR in [#336](https://github.com/tr/ras-search_ai-conversations/pull/336)
* AB#2086024 - QA - V2  500 internal server error by @TylerJacksonTR in [#335](https://github.com/tr/ras-search_ai-conversations/pull/335)
* AB#2087022: BUG: QA - V3 testing Able to hit any number of follow up's though the initial question is in progress by @skif-sarmat in [#337](https://github.com/tr/ras-search_ai-conversations/pull/337)
* AB#2087520 - QA - V3 testing - conversation metadata attribute "data_retention" is always 365_days by @TylerJacksonTR in [#338](https://github.com/tr/ras-search_ai-conversations/pull/338)


## v0.13.6 (2024-10-25)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.6)

### What's Changed

* AB#2086017 - QA - V3 testing The custom field is empty in GET conversation api by @TylerJacksonTR in [#333](https://github.com/tr/ras-search_ai-conversations/pull/333)


## v0.13.5 (2024-10-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.5)

### What's Changed

* Update CHANGELOG.md file for QA version v0.13.2 by @BhupeshParakhTR in [#329](https://github.com/tr/ras-search_ai-conversations/pull/329)
* AB#2085779 - AI Westlaw - V3 Changes - Skill Routing Step is Being Run on Follow Up Questions by @TylerJacksonTR in [#331](https://github.com/tr/ras-search_ai-conversations/pull/331)
* AB#2086629 - AI Westlaw - V3 Changes - Regardless of the user input ,… by @TylerJacksonTR in [#332](https://github.com/tr/ras-search_ai-conversations/pull/332)


## v0.13.2 (2024-10-16)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.13.2)

### What's Changed

* Ab#2070973 new endpoints by @JoshGrittnerTR in [#310](https://github.com/tr/ras-search_ai-conversations/pull/310)
* Update CHANGELOG.md file for QA version v0.12.31 by @BhupeshParakhTR in [#322](https://github.com/tr/ras-search_ai-conversations/pull/322)
* Select a Skill and V3 Implementation/Refactor by @TylerJacksonTR in [#324](https://github.com/tr/ras-search_ai-conversations/pull/324)
* Swagger example updates for v3 by @JoshGrittnerTR in [#326](https://github.com/tr/ras-search_ai-conversations/pull/326)
* Ab#2081092 required header error by @JoshGrittnerTR in [#321](https://github.com/tr/ras-search_ai-conversations/pull/321)


## v0.12.31 (2024-10-09)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.31)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.29 by @BhupeshParakhTR in [#320](https://github.com/tr/ras-search_ai-conversations/pull/320)


## v0.12.29 (2024-10-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.29)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.26 by @BhupeshParakhTR in [#317](https://github.com/tr/ras-search_ai-conversations/pull/317)
* AB#2079896 : Fixing user classification and user session info data on BS detector by @BhupeshParakhTR in [#316](https://github.com/tr/ras-search_ai-conversations/pull/316)
* AB#2079963- poetry update for BS detect changes by @akshathaaithalrTR in [#318](https://github.com/tr/ras-search_ai-conversations/pull/318)
* updating poetry for BS detect by @BhupeshParakhTR in [#319](https://github.com/tr/ras-search_ai-conversations/pull/319)

### New Contributors

* @akshathaaithalrTR made their first contribution in [#318](https://github.com/tr/ras-search_ai-conversations/pull/318)


## v0.12.26 (2024-10-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.26)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.25 by @BhupeshParakhTR in [#314](https://github.com/tr/ras-search_ai-conversations/pull/314)
* AB#2079825 - Fixing the DDB TTL for BS detect to 1DAY by @BhupeshParakhTR in [#315](https://github.com/tr/ras-search_ai-conversations/pull/315)


## v0.12.25 (2024-09-27)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.25)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.23 by @BhupeshParakhTR in [#311](https://github.com/tr/ras-search_ai-conversations/pull/311)
* AB#2076815 - Upgraded the on_event to lifespan event in fastAPI by @BhupeshParakhTR in [#312](https://github.com/tr/ras-search_ai-conversations/pull/312)
* AB#2076359 - Resubmit Runs Intent Again by @TylerJacksonTR in [#313](https://github.com/tr/ras-search_ai-conversations/pull/313)


## v0.12.23 (2024-09-19)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.23)

### What's Changed

* Ab#2037055 v3 by @JoshGrittnerTR in [#280](https://github.com/tr/ras-search_ai-conversations/pull/280)
* Update CHANGELOG.md file for QA version v0.12.17 by @BhupeshParakhTR in [#304](https://github.com/tr/ras-search_ai-conversations/pull/304)
* Qcbs task name fix by @JoshGrittnerTR in [#305](https://github.com/tr/ras-search_ai-conversations/pull/305)
* NO_TICKET: FIX: Fixed failed unit tests by @skif-sarmat in [#307](https://github.com/tr/ras-search_ai-conversations/pull/307)
* AB#2067824: FEATURE: AI Conversations - Handle Large Inputs in a Generic Way by @skif-sarmat in [#308](https://github.com/tr/ras-search_ai-conversations/pull/308)
* Bug fixes by @JoshGrittnerTR in [#309](https://github.com/tr/ras-search_ai-conversations/pull/309)


## v0.12.17 (2024-09-05)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.17)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.16 by @BhupeshParakhTR in [#300](https://github.com/tr/ras-search_ai-conversations/pull/300)
* AB#2067299: FEATURE: Integrate BS Detector wheel file for RAG functionality by @skif-sarmat in [#302](https://github.com/tr/ras-search_ai-conversations/pull/302)


## v0.12.16 (2024-09-03)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.16)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.15 by @BhupeshParakhTR in [#294](https://github.com/tr/ras-search_ai-conversations/pull/294)
* AB#2035531 - Long Delay for Jurisdictions Bug by @TylerJacksonTR in [#298](https://github.com/tr/ras-search_ai-conversations/pull/298)


## v0.12.15 (2024-08-30)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.15)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.12 by @BhupeshParakhTR in [#288](https://github.com/tr/ras-search_ai-conversations/pull/288)
* AB#2067768 Fixed error message for conversation metadata updation by @AmitChoudharyTR in [#291](https://github.com/tr/ras-search_ai-conversations/pull/291)
* added skipping conversation validation if profile metadata has conten… by @DongKimTR in [#289](https://github.com/tr/ras-search_ai-conversations/pull/289)
* AB#2067768: MD bug fix by @JoshGrittnerTR in [#293](https://github.com/tr/ras-search_ai-conversations/pull/293)

### New Contributors

* @DongKimTR made their first contribution in [#289](https://github.com/tr/ras-search_ai-conversations/pull/289)


## v0.12.12 (2024-08-26)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.12)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.11 by @BhupeshParakhTR in [#286](https://github.com/tr/ras-search_ai-conversations/pull/286)
* refactor cache logic. by @scottberres-tr in [#287](https://github.com/tr/ras-search_ai-conversations/pull/287)


## v0.12.11 (2024-08-25)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.11)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.10 by @BhupeshParakhTR in [#284](https://github.com/tr/ras-search_ai-conversations/pull/284)
* cached conversations are no real worker task(s) by @scottberres-tr in [#285](https://github.com/tr/ras-search_ai-conversations/pull/285)


## v0.12.10 (2024-08-22)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.10)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.9 by @BhupeshParakhTR in [#283](https://github.com/tr/ras-search_ai-conversations/pull/283)


## v0.12.9 (2024-08-22)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.8 by @BhupeshParakhTR in [#282](https://github.com/tr/ras-search_ai-conversations/pull/282)


## v0.12.8 (2024-08-21)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.8)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.5 by @BhupeshParakhTR in [#281](https://github.com/tr/ras-search_ai-conversations/pull/281)


## v0.12.5 (2024-08-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.5)

### What's Changed

* Update CHANGELOG.md file for QA version v0.12.3 by @BhupeshParakhTR in [#279](https://github.com/tr/ras-search_ai-conversations/pull/279)


## v0.12.3 (2024-08-15)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.12.3)

### What's Changed

* Update CHANGELOG.md file for QA version v0.11.8 by @BhupeshParakhTR in [#272](https://github.com/tr/ras-search_ai-conversations/pull/272)
* AB#2041014 - updated poetry for intent profile in span by @BhupeshParakhTR in [#273](https://github.com/tr/ras-search_ai-conversations/pull/273)
* 2004154: Getting 500 Internal server error when user sending invalid conversation_entry_id for update conversation entry metadata by @abduakhatov in [#269](https://github.com/tr/ras-search_ai-conversations/pull/269)
* AB#2039522 - Upgrade to Python 3.11 - RAS Conversations by @TylerJacksonTR in [#274](https://github.com/tr/ras-search_ai-conversations/pull/274)
* Conversation Cache by @scottberres-tr in [#275](https://github.com/tr/ras-search_ai-conversations/pull/275)


## v0.11.8 (2024-08-02)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.11.8)

### What's Changed

* Update CHANGELOG.md file for QA version v0.11.4 by @ManojPradhanTR in [#267](https://github.com/tr/ras-search_ai-conversations/pull/267)
* Update CHANGELOG.md file for QA version v0.11.6 by @ManojPradhanTR in [#268](https://github.com/tr/ras-search_ai-conversations/pull/268)
* Testing changelog changes by @BhupeshParakhTR in [#270](https://github.com/tr/ras-search_ai-conversations/pull/270)
* hitting changelog functionality by @BhupeshParakhTR in [#271](https://github.com/tr/ras-search_ai-conversations/pull/271)


## v0.11.6 (2024-07-31)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.11.6)

### What's Changed

* Fix & Test: 404 error for unexsting conversation by @abduakhatov in [#261](https://github.com/tr/ras-search_ai-conversations/pull/261)
* AB#2004152 Bug: Fixed error code for invalid params by @AmitChoudharyTR in [#264](https://github.com/tr/ras-search_ai-conversations/pull/264)
* Ab#2036517 disclaimer element by @JoshGrittnerTR in [#265](https://github.com/tr/ras-search_ai-conversations/pull/265)


## v0.11.4 (2024-07-31)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.11.4)

### What's Changed

* Update CHANGELOG.md file for QA version v0.10.16 by @ManojPradhanTR in [#256](https://github.com/tr/ras-search_ai-conversations/pull/256)
* Update CHANGELOG.md file for QA version v0.11.2 by @ManojPradhanTR in [#257](https://github.com/tr/ras-search_ai-conversations/pull/257)
* AB#2020420:BUG:  Added additional context to error details by @skif-sarmat in [#262](https://github.com/tr/ras-search_ai-conversations/pull/262)
* Ab#2014184 bs detector by @JoshGrittnerTR in [#263](https://github.com/tr/ras-search_ai-conversations/pull/263)


## v0.11.2 (2024-07-16)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.11.2)

### What's Changed

* Updated poetry lock for latest utils package by @AmitChoudharyTR in [#251](https://github.com/tr/ras-search_ai-conversations/pull/251)
* 1952374 qa 500 response fix by @abduakhatov in [#252](https://github.com/tr/ras-search_ai-conversations/pull/252)
* AB#2009456 - fixing the post deploy pipleine build fails by @BhupeshParakhTR in [#253](https://github.com/tr/ras-search_ai-conversations/pull/253)
* Upgrade poetry for stats enhancement endpoint models by @ChristianEggersTR in [#254](https://github.com/tr/ras-search_ai-conversations/pull/254)
* Ab#1911140 cancel feature by @TylerJacksonTR in [#246](https://github.com/tr/ras-search_ai-conversations/pull/246)
* AB#1963615: BUG: returned right status in case a conversation was not found by @skif-sarmat in [#255](https://github.com/tr/ras-search_ai-conversations/pull/255)

### New Contributors

* @AmitChoudharyTR made their first contribution in [#251](https://github.com/tr/ras-search_ai-conversations/pull/251)
* @skif-sarmat made their first contribution in [#255](https://github.com/tr/ras-search_ai-conversations/pull/255)


## v0.10.16 (2024-07-16)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.16)

### What's Changed

* Update CHANGELOG.md file for QA version v0.10.15 by @ManojPradhanTR in [#247](https://github.com/tr/ras-search_ai-conversations/pull/247)
* Tests updated based on new conv v2 request validation by @abduakhatov in [#245](https://github.com/tr/ras-search_ai-conversations/pull/245)
* Revert "Tests updated based on new conv v2 request validation" by @abduakhatov in [#249](https://github.com/tr/ras-search_ai-conversations/pull/249)


## v0.10.15 (2024-07-05)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.15)

### What's Changed

* Update CHANGELOG.md file for QA version v0.10.9 by @ManojPradhanTR in [#240](https://github.com/tr/ras-search_ai-conversations/pull/240)
* Conversations Statistics endpoint enhancements by @ChristianEggersTR in [#242](https://github.com/tr/ras-search_ai-conversations/pull/242)
* update lockfile by @ChristianEggersTR in [#243](https://github.com/tr/ras-search_ai-conversations/pull/243)
* AB#1928271 - Cleanup chat profile and queue files in test using AnswerProfile by @BhupeshParakhTR in [#238](https://github.com/tr/ras-search_ai-conversations/pull/238)
* Tr golden docker image by @scottberres-tr in [#244](https://github.com/tr/ras-search_ai-conversations/pull/244)


## v0.10.9 (2024-06-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.10.8 by @ManojPradhanTR in [#237](https://github.com/tr/ras-search_ai-conversations/pull/237)
* AI Disclaimer now profile driven. by @scottberres-tr in [#239](https://github.com/tr/ras-search_ai-conversations/pull/239)


## v0.10.8 (2024-06-21)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.8)

### What's Changed

* Update CHANGELOG.md file for QA version v0.10.7 by @ManojPradhanTR in [#236](https://github.com/tr/ras-search_ai-conversations/pull/236)


## v0.10.7 (2024-06-21)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.7)

### What's Changed

* Update CHANGELOG.md file for QA version v0.9.6 by @ManojPradhanTR in [#230](https://github.com/tr/ras-search_ai-conversations/pull/230)
* AB#1952354 restrict metadata updates on put endpoints by @AndreiLosikTR in [#231](https://github.com/tr/ras-search_ai-conversations/pull/231)
* Update CHANGELOG.md file for QA version v0.10.1 by @ManojPradhanTR in [#233](https://github.com/tr/ras-search_ai-conversations/pull/233)
* Invalid query parameter for conversation-statistics by @WojciechMaczkaTR in [#232](https://github.com/tr/ras-search_ai-conversations/pull/232)
* Fix: Getting 200 success status with blank response on invalid profile name by @abduakhatov in [#227](https://github.com/tr/ras-search_ai-conversations/pull/227)
* appending disclaimer on history if it doesn't exist. by @scottberres-tr in [#235](https://github.com/tr/ras-search_ai-conversations/pull/235)

### New Contributors

* @abduakhatov made their first contribution in [#227](https://github.com/tr/ras-search_ai-conversations/pull/227)


## v0.10.1 (2024-06-14)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.10.1)

### What's Changed

* AB#1812287 - adding client id filter for conversation list api by @BhupeshParakhTR in [#226](https://github.com/tr/ras-search_ai-conversations/pull/226)
* updation of poetry lock file by @BhupeshParakhTR in [#228](https://github.com/tr/ras-search_ai-conversations/pull/228)


## v0.9.6 (2024-06-12)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.9.6)

### What's Changed

* Update CHANGELOG.md file for QA version v0.9.5 by @ManojPradhanTR in [#224](https://github.com/tr/ras-search_ai-conversations/pull/224)
* Ab#1942555 ttl metadata not updated by @WojciechMaczkaTR in [#225](https://github.com/tr/ras-search_ai-conversations/pull/225)

### New Contributors

* @WojciechMaczkaTR made their first contribution in [#225](https://github.com/tr/ras-search_ai-conversations/pull/225)


## v0.9.5 (2024-06-06)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.9.5)

### What's Changed

* Update CHANGELOG.md file for QA version v0.9.3 by @ManojPradhanTR in [#223](https://github.com/tr/ras-search_ai-conversations/pull/223)


## v0.9.3 (2024-06-05)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.9.3)

### What's Changed

* Update CHANGELOG.md file for QA version v0.9.2 by @ManojPradhanTR in [#222](https://github.com/tr/ras-search_ai-conversations/pull/222)


## v0.9.2 (2024-06-05)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.9.2)

### What's Changed

* Update CHANGELOG.md file for QA version v0.9.0 by @ManojPradhanTR in [#220](https://github.com/tr/ras-search_ai-conversations/pull/220)
* AB#1931722 - Update Celery Config visibility_timeout Values by @TylerJacksonTR in [#218](https://github.com/tr/ras-search_ai-conversations/pull/218)
* AB#1952028 - Conversation Title updated with every Follow Up Question by @BhupeshParakhTR in [#221](https://github.com/tr/ras-search_ai-conversations/pull/221)


## v0.9.0 (2024-05-30)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.9.0)

### What's Changed

* Update CHANGELOG.md file for QA version v0.8.3 by @ManojPradhanTR in [#217](https://github.com/tr/ras-search_ai-conversations/pull/217)
* AB#1939400 - Update the metadata status for follow-ups  by @BhupeshParakhTR in [#219](https://github.com/tr/ras-search_ai-conversations/pull/219)


## v0.8.3 (2024-05-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.8.3)

### What's Changed

* Update CHANGELOG.md file for QA version v0.8.2 by @ManojPradhanTR in [#216](https://github.com/tr/ras-search_ai-conversations/pull/216)


## v0.8.2 (2024-05-23)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.8.2)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.20 by @ManojPradhanTR in [#213](https://github.com/tr/ras-search_ai-conversations/pull/213)
* Update CHANGELOG.md file for QA version v0.8.1 by @ManojPradhanTR in [#214](https://github.com/tr/ras-search_ai-conversations/pull/214)
* visibility timeout increase by @JoshGrittnerTR in [#215](https://github.com/tr/ras-search_ai-conversations/pull/215)


## v0.8.1 (2024-05-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.8.1)

### What's Changed

* AB#1910508 - Increase Test Coverage for Unit Tests of the Conversations Frontend Microservice by @BhupeshParakhTR in [#208](https://github.com/tr/ras-search_ai-conversations/pull/208)
* AB#1915700 - PUT Conversation Entry Metadata returns 500 instead of 404 by @BhupeshParakhTR in [#212](https://github.com/tr/ras-search_ai-conversations/pull/212)


## v0.7.20 (2024-05-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.20)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.19 by @ManojPradhanTR in [#209](https://github.com/tr/ras-search_ai-conversations/pull/209)
* AB#1913481 Update percent complete metadata field by @TylerJacksonTR in [#210](https://github.com/tr/ras-search_ai-conversations/pull/210)


## v0.7.19 (2024-05-13)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.19)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.16 by @ManojPradhanTR in [#206](https://github.com/tr/ras-search_ai-conversations/pull/206)
* Update CHANGELOG.md file for QA version v0.7.17 by @ManojPradhanTR in [#207](https://github.com/tr/ras-search_ai-conversations/pull/207)


## v0.7.17 (2024-05-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.17)




## v0.7.16 (2024-05-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.16)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.12 by @ManojPradhanTR in [#203](https://github.com/tr/ras-search_ai-conversations/pull/203)
* Update CHANGELOG.md file for QA version v0.7.15 by @ManojPradhanTR in [#204](https://github.com/tr/ras-search_ai-conversations/pull/204)
* AB#1910551 Code analysis of Snyk Flaws by @LathaKalathuruTR in [#205](https://github.com/tr/ras-search_ai-conversations/pull/205)


## v0.7.15 (2024-05-06)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.15)

### What's Changed

* Ab#1911144 GET Conversations in Datadog should display Chat Profile  by @BhupeshParakhTR in [#198](https://github.com/tr/ras-search_ai-conversations/pull/198)
* AB#1910551 Snyk_Fixes by @LathaKalathuruTR in [#200](https://github.com/tr/ras-search_ai-conversations/pull/200)
* Ab#1906821Updating poetry lock file for dynamo db changes  by @BhupeshParakhTR in [#202](https://github.com/tr/ras-search_ai-conversations/pull/202)


## v0.7.12 (2024-05-06)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.12)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.11 by @ManojPradhanTR in [#201](https://github.com/tr/ras-search_ai-conversations/pull/201)


## v0.7.11 (2024-04-30)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.11)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.9 by @ManojPradhanTR in [#196](https://github.com/tr/ras-search_ai-conversations/pull/196)
* Update CHANGELOG.md file for QA version v0.7.10 by @ManojPradhanTR in [#197](https://github.com/tr/ras-search_ai-conversations/pull/197)
* Ab#1910507 Updated the lock file for shared conversation by @BhupeshParakhTR in [#199](https://github.com/tr/ras-search_ai-conversations/pull/199)


## v0.7.10 (2024-04-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.10)

### What's Changed

* Use original profile by @JoshGrittnerTR in [#195](https://github.com/tr/ras-search_ai-conversations/pull/195)


## v0.7.9 (2024-04-24)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.7.4 by @ManojPradhanTR in [#193](https://github.com/tr/ras-search_ai-conversations/pull/193)
* 1896914 encrypted redis cluster update by @SasmitaSahooTR in [#188](https://github.com/tr/ras-search_ai-conversations/pull/188)
* AB#1906808- Fixing failing unit test cases for frontend microservice by @BhupeshParakhTR in [#189](https://github.com/tr/ras-search_ai-conversations/pull/189)
* AB#1896914 Revert new redis by @JoshGrittnerTR in [#194](https://github.com/tr/ras-search_ai-conversations/pull/194)


## v0.7.4 (2024-04-18)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.7.4)

### What's Changed

* Updating python-cicd-workflows (No more Veracode) by @workflow-template-automation in [#182](https://github.com/tr/ras-search_ai-conversations/pull/182)
* Update CHANGELOG.md file for QA version v0.6.14 by @ManojPradhanTR in [#185](https://github.com/tr/ras-search_ai-conversations/pull/185)
* AB#1906821 Added user chat profile into datadog trace span info for frontend microservice by @BhupeshParakhTR in [#186](https://github.com/tr/ras-search_ai-conversations/pull/186)
* AB#1909262 fix asset insight id by @AndreiLosikTR in [#191](https://github.com/tr/ras-search_ai-conversations/pull/191)
* AB#1908341: Hide RAG pipeline output by @JoshGrittnerTR in [#192](https://github.com/tr/ras-search_ai-conversations/pull/192)

### New Contributors

* @ManojPradhanTR made their first contribution in [#185](https://github.com/tr/ras-search_ai-conversations/pull/185)
* @BhupeshParakhTR made their first contribution in [#186](https://github.com/tr/ras-search_ai-conversations/pull/186)


## v0.6.14 (2024-04-09)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.14)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.12 by @bharatTR in [#179](https://github.com/tr/ras-search_ai-conversations/pull/179)
* Ab#1901170 max followup by @JoshGrittnerTR in [#180](https://github.com/tr/ras-search_ai-conversations/pull/180)
* Ab#1905751 bug follow up jurisdiction override by @JoshGrittnerTR in [#184](https://github.com/tr/ras-search_ai-conversations/pull/184)


## v0.6.12 (2024-03-26)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.12)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.10 by @bharatTR in [#178](https://github.com/tr/ras-search_ai-conversations/pull/178)
* Ab#968072 force a new deploy with 1.14.3 cumulus by @MatthewOHaraTR in [#148](https://github.com/tr/ras-search_ai-conversations/pull/148)


## v0.6.10 (2024-03-14)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.10)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.9 by @bharatTR in [#176](https://github.com/tr/ras-search_ai-conversations/pull/176)
* Ab#1899649 content types exclude by @JoshGrittnerTR in [#177](https://github.com/tr/ras-search_ai-conversations/pull/177)


## v0.6.9 (2024-03-14)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.9)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.4 by @bharatTR in [#173](https://github.com/tr/ras-search_ai-conversations/pull/173)
* Optional filter for solution profile by @scottberres-tr in [#175](https://github.com/tr/ras-search_ai-conversations/pull/175)
* AB#189867 Changes for plexus cert ingress and refactor resource annot… by @SasmitaSahooTR in [#174](https://github.com/tr/ras-search_ai-conversations/pull/174)
* Ab#1896913 swagger improvements by @JoshGrittnerTR in [#172](https://github.com/tr/ras-search_ai-conversations/pull/172)


## v0.6.4 (2024-03-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.4)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.3 by @bharatTR in [#171](https://github.com/tr/ras-search_ai-conversations/pull/171)


## v0.6.3 (2024-03-07)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.3)

### What's Changed

* Update CHANGELOG.md file for QA version v0.6.2 by @bharatTR in [#170](https://github.com/tr/ras-search_ai-conversations/pull/170)


## v0.6.2 (2024-03-07)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.6.2)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.49 by @bharatTR in [#168](https://github.com/tr/ras-search_ai-conversations/pull/168)
* Feature/swagger UI updates by @JoshGrittnerTR in [#159](https://github.com/tr/ras-search_ai-conversations/pull/159)
* Revert "Feature/swagger UI updates" by @JoshGrittnerTR in [#169](https://github.com/tr/ras-search_ai-conversations/pull/169)
* Content console profiles by @scottberres-tr in [#163](https://github.com/tr/ras-search_ai-conversations/pull/163)


## v0.5.49 (2024-03-04)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.49)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.48 by @bharatTR in [#167](https://github.com/tr/ras-search_ai-conversations/pull/167)


## v0.5.48 (2024-03-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.48)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.47 by @bharatTR in [#166](https://github.com/tr/ras-search_ai-conversations/pull/166)
* Ras shared utils 0.5.137 by @TylerJacksonTR in [#165](https://github.com/tr/ras-search_ai-conversations/pull/165)


## v0.5.47 (2024-03-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.47)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.45 by @bharatTR in [#164](https://github.com/tr/ras-search_ai-conversations/pull/164)


## v0.5.45 (2024-02-28)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.45)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.43 by @bharatTR in [#161](https://github.com/tr/ras-search_ai-conversations/pull/161)
* AB#1893656 Fix tests by @bharatTR in [#156](https://github.com/tr/ras-search_ai-conversations/pull/156)


## v0.5.43 (2024-02-27)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.43)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.42 by @bharatTR in [#158](https://github.com/tr/ras-search_ai-conversations/pull/158)
* AB#1884318: Adding cobalt UDS header into session dict by @JoshGrittnerTR in [#160](https://github.com/tr/ras-search_ai-conversations/pull/160)


## v0.5.42 (2024-02-26)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.42)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.40 by @bharatTR in [#155](https://github.com/tr/ras-search_ai-conversations/pull/155)
* AB#887289 Max conversation entry and lock time for a conversation by @bharatTR in [#147](https://github.com/tr/ras-search_ai-conversations/pull/147)


## v0.5.40 (2024-02-21)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.40)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.39 by @bharatTR in [#153](https://github.com/tr/ras-search_ai-conversations/pull/153)
* Ddb consistent read by @JoshGrittnerTR in [#154](https://github.com/tr/ras-search_ai-conversations/pull/154)


## v0.5.39 (2024-02-16)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.39)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.37 by @bharatTR in [#150](https://github.com/tr/ras-search_ai-conversations/pull/150)
* AB#1865601 Add tests by @bharatTR in [#152](https://github.com/tr/ras-search_ai-conversations/pull/152)


## v0.5.37 (2024-02-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.37)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.36 by @bharatTR in [#149](https://github.com/tr/ras-search_ai-conversations/pull/149)


## v0.5.36 (2024-02-07)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.36)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.33 by @bharatTR in [#145](https://github.com/tr/ras-search_ai-conversations/pull/145)
* Update CHANGELOG.md file for QA version v0.5.34 by @bharatTR in [#146](https://github.com/tr/ras-search_ai-conversations/pull/146)
* AB#1881301 Remove duplicate columns from DDB by @bharatTR in [#127](https://github.com/tr/ras-search_ai-conversations/pull/127)


## v0.5.34 (2024-02-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.34)




## v0.5.33 (2024-02-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.33)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.32 by @bharatTR in [#144](https://github.com/tr/ras-search_ai-conversations/pull/144)


## v0.5.32 (2024-02-01)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.32)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.28 by @bharatTR in [#139](https://github.com/tr/ras-search_ai-conversations/pull/139)
* AB#1880265 Add favourite conversation support for Checkpoint by @bharatTR in [#126](https://github.com/tr/ras-search_ai-conversations/pull/126)
* AB#1882733 switch events endpoint to v2 by @AndreiLosikTR in [#134](https://github.com/tr/ras-search_ai-conversations/pull/134)
* using constants by @scottberres-tr in [#141](https://github.com/tr/ras-search_ai-conversations/pull/141)
* AB#1883395: Feature/statistics endpoint by @JoshGrittnerTR in [#142](https://github.com/tr/ras-search_ai-conversations/pull/142)


## v0.5.28 (2024-01-25)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.28)

### What's Changed

* Ab#1877422 get profile from conv md by @JoshGrittnerTR in [#128](https://github.com/tr/ras-search_ai-conversations/pull/128)
* Update CHANGELOG.md file for QA version v0.5.19 by @bharatTR in [#131](https://github.com/tr/ras-search_ai-conversations/pull/131)
* Adhere to the Container and Kubernetes Labels Standard by @MatthewOHaraTR in [#135](https://github.com/tr/ras-search_ai-conversations/pull/135)
* Updating shared python utils version to support claims explorer by @TylerJacksonTR in [#136](https://github.com/tr/ras-search_ai-conversations/pull/136)
* Initial followup fix by @scottberres-tr in [#137](https://github.com/tr/ras-search_ai-conversations/pull/137)
* Updating v1 conversation type required param by @JoshGrittnerTR in [#138](https://github.com/tr/ras-search_ai-conversations/pull/138)

### New Contributors

* @MatthewOHaraTR made their first contribution in [#135](https://github.com/tr/ras-search_ai-conversations/pull/135)
* @TylerJacksonTR made their first contribution in [#136](https://github.com/tr/ras-search_ai-conversations/pull/136)


## v0.5.19 (2024-01-16)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.19)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.17 by @bharatTR in [#124](https://github.com/tr/ras-search_ai-conversations/pull/124)
* Making profile lookup case-insensitive (PL storing profiles in upper … by @JoshGrittnerTR in [#125](https://github.com/tr/ras-search_ai-conversations/pull/125)
* AB#1882006 set conversation action type to intent when it is going to be executed by @AndreiLosikTR in [#129](https://github.com/tr/ras-search_ai-conversations/pull/129)


## v0.5.17 (2023-12-20)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.17)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.3 by @bharatTR in [#121](https://github.com/tr/ras-search_ai-conversations/pull/121)
* requiring product and product view headers. by @scottberres-tr in [#122](https://github.com/tr/ras-search_ai-conversations/pull/122)
* adding context from session to each v2 call by @scottberres-tr in [#123](https://github.com/tr/ras-search_ai-conversations/pull/123)


## v0.5.3 (2023-12-14)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.3)

### What's Changed

* Update CHANGELOG.md file for QA version v0.5.0 by @bharatTR in [#119](https://github.com/tr/ras-search_ai-conversations/pull/119)
* Update for non intent resolver profiles in v2 by @JoshGrittnerTR in [#120](https://github.com/tr/ras-search_ai-conversations/pull/120)


## v0.5.0 (2023-12-13)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.5.0)

### What's Changed

* Keycite by @JoshGrittnerTR in [#107](https://github.com/tr/ras-search_ai-conversations/pull/107)
* AB#1869296 Validate custom field for westlaw profile by @bharatTR in [#101](https://github.com/tr/ras-search_ai-conversations/pull/101)
* Fix arugument name by @bharatTR in [#108](https://github.com/tr/ras-search_ai-conversations/pull/108)
* Update CHANGELOG.md file for QA version v0.4.9 by @bharatTR in [#113](https://github.com/tr/ras-search_ai-conversations/pull/113)
* AB#1865812 add request validation exception handler to return 400 status code by @AndreiLosikTR in [#114](https://github.com/tr/ras-search_ai-conversations/pull/114)
* writing new event to track total time across the queue. by @scottberres-tr in [#117](https://github.com/tr/ras-search_ai-conversations/pull/117)
* January Release by @scottberres-tr in [#118](https://github.com/tr/ras-search_ai-conversations/pull/118)

### New Contributors

* @AndreiLosikTR made their first contribution in [#114](https://github.com/tr/ras-search_ai-conversations/pull/114)


## v0.4.9 (2023-12-08)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.4.9)

### What's Changed

* AB#1869297-Resolving CORS issue and AB#1869297-error handling for all v1 and v2 get apis by @satyadashTR in [#103](https://github.com/tr/ras-search_ai-conversations/pull/103)
* 1873728 cleanup v1 cobalt execute cobalt search by @SasmitaSahooTR in [#104](https://github.com/tr/ras-search_ai-conversations/pull/104)
* Update CHANGELOG.md file for new QA version v0.4.6 by @bharatTR in [#111](https://github.com/tr/ras-search_ai-conversations/pull/111)


## v0.4.6 (2023-12-06)

[GitHub release](https://github.com/tr/ras-search_ai-conversations/releases/tag/v0.4.6)

### What's Changed

* Python cicd workflows 1690984762 by @AdamPetersonTR in [#2](https://github.com/tr/ras-search_ai-conversations/pull/2)
* Conversation metadata by @JoshGrittnerTR in [#3](https://github.com/tr/ras-search_ai-conversations/pull/3)
* Entry refactor by @JoshGrittnerTR in [#5](https://github.com/tr/ras-search_ai-conversations/pull/5)
* Updating Lock file for shared utils by @JoshGrittnerTR in [#4](https://github.com/tr/ras-search_ai-conversations/pull/4)
* Added redirection to checkpoint queue for checkpoint profile by @RaviKadiwalaTR in [#7](https://github.com/tr/ras-search_ai-conversations/pull/7)
* Ab#1837361 by @JoshGrittnerTR in [#8](https://github.com/tr/ras-search_ai-conversations/pull/8)
* Wl3 1 by @JoshGrittnerTR in [#11](https://github.com/tr/ras-search_ai-conversations/pull/11)
* Conv list api us us 1825705 by @satyadashTR in [#10](https://github.com/tr/ras-search_ai-conversations/pull/10)
* AB#1812289 Delete conversation and metadata endpoint by @HarshitSrivastavaTR in [#9](https://github.com/tr/ras-search_ai-conversations/pull/9)
* AB#1839942 Handle unprocessed items and exception handling by @HarshitSrivastavaTR in [#12](https://github.com/tr/ras-search_ai-conversations/pull/12)
* AB#1838130 Added for to trigger a build to take the enable sysdig bak… by @SasmitaSahooTR in [#14](https://github.com/tr/ras-search_ai-conversations/pull/14)
* 1838130 trigger build sysdig enable by @SasmitaSahooTR in [#15](https://github.com/tr/ras-search_ai-conversations/pull/15)
* Adding dictionary to switch between fields and embedding models by @JohnEngelhart in [#13](https://github.com/tr/ras-search_ai-conversations/pull/13)
* Update shared utils by @JoshGrittnerTR in [#20](https://github.com/tr/ras-search_ai-conversations/pull/20)
* Update open_search.py by @JohnEngelhart in [#24](https://github.com/tr/ras-search_ai-conversations/pull/24)
* Python utils update by @JoshGrittnerTR in [#25](https://github.com/tr/ras-search_ai-conversations/pull/25)
* AB#1841258 Changes for to enable veracode scan. by @SasmitaSahooTR in [#22](https://github.com/tr/ras-search_ai-conversations/pull/22)
* AB#1842503 Fix prod deployment by @HarshitSrivastavaTR in [#26](https://github.com/tr/ras-search_ai-conversations/pull/26)
* AB#1842502 Added for sysdig issue fix by @SasmitaSahooTR in [#27](https://github.com/tr/ras-search_ai-conversations/pull/27)
* Keycite API contract Implementation us-1841031 by @satyadashTR in [#23](https://github.com/tr/ras-search_ai-conversations/pull/23)
* 1842502 sysdig fix by @SasmitaSahooTR in [#30](https://github.com/tr/ras-search_ai-conversations/pull/30)
* Email support by @JoshGrittnerTR in [#28](https://github.com/tr/ras-search_ai-conversations/pull/28)
* Fixing follow up questions error by @JoshGrittnerTR in [#33](https://github.com/tr/ras-search_ai-conversations/pull/33)
* Sysdig setuptool fix by @SasmitaSahooTR in [#35](https://github.com/tr/ras-search_ai-conversations/pull/35)
* Use cpu metric for scaling by @bharatTR in [#21](https://github.com/tr/ras-search_ai-conversations/pull/21)
* testspec addition to the repo by @AdamPetersonTR in [#37](https://github.com/tr/ras-search_ai-conversations/pull/37)
* readme updated by @scottberres-tr in [#40](https://github.com/tr/ras-search_ai-conversations/pull/40)
* default header to blue, and lower case it by @scottberres-tr in [#41](https://github.com/tr/ras-search_ai-conversations/pull/41)
* veracode sca fix by @SasmitaSahooTR in [#42](https://github.com/tr/ras-search_ai-conversations/pull/42)
* AB#1860615 Retrieve Conversation Entry returns more email specific details by @HarshitSrivastavaTR in [#39](https://github.com/tr/ras-search_ai-conversations/pull/39)
* AB#1842494:Result_Viewed_Timestamp_In_GET_ Conversation_Entry by @LathaKalathuruTR in [#36](https://github.com/tr/ras-search_ai-conversations/pull/36)
* Poe the poet wired up for local testing by @scottberres-tr in [#43](https://github.com/tr/ras-search_ai-conversations/pull/43)
* returning better errors by @scottberres-tr in [#44](https://github.com/tr/ras-search_ai-conversations/pull/44)
* Updated docker file by @scottberres-tr in [#45](https://github.com/tr/ras-search_ai-conversations/pull/45)
* documentation update for running smoke tests locally by @AdamPetersonTR in [#46](https://github.com/tr/ras-search_ai-conversations/pull/46)
* routing fix by @scottberres-tr in [#47](https://github.com/tr/ras-search_ai-conversations/pull/47)
* ras logger updated by @scottberres-tr in [#53](https://github.com/tr/ras-search_ai-conversations/pull/53)
* returning updated status updates by @scottberres-tr in [#54](https://github.com/tr/ras-search_ai-conversations/pull/54)
* event cleanup by @scottberres-tr in [#56](https://github.com/tr/ras-search_ai-conversations/pull/56)
* AB#1860615 Fix bug with In-Progress conversations by @HarshitSrivastavaTR in [#51](https://github.com/tr/ras-search_ai-conversations/pull/51)
* entry id used instead of conversation id by @scottberres-tr in [#58](https://github.com/tr/ras-search_ai-conversations/pull/58)
* new utils version by @scottberres-tr in [#59](https://github.com/tr/ras-search_ai-conversations/pull/59)
* 1860612/add email sent every conversation entry by @LathaKalathuruTR in [#57](https://github.com/tr/ras-search_ai-conversations/pull/57)
* Updated shared utile to 0.4.11 by @RaviKadiwalaTR in [#62](https://github.com/tr/ras-search_ai-conversations/pull/62)
* quick fix for app to start error code parsing by @scottberres-tr in [#65](https://github.com/tr/ras-search_ai-conversations/pull/65)
* Prod pod increase by @JoshGrittnerTR in [#63](https://github.com/tr/ras-search_ai-conversations/pull/63)
* Set python version for sca by @KevinHuberTR in [#66](https://github.com/tr/ras-search_ai-conversations/pull/66)
* Revert "Set python version for sca" by @KevinHuberTR in [#67](https://github.com/tr/ras-search_ai-conversations/pull/67)
* updated shared utils for events timestamp by @scottberres-tr in [#68](https://github.com/tr/ras-search_ai-conversations/pull/68)
* AB#1865249 Updating shared util version by @LathaKalathuruTR in [#69](https://github.com/tr/ras-search_ai-conversations/pull/69)
* Local events url by @JoshGrittnerTR in [#70](https://github.com/tr/ras-search_ai-conversations/pull/70)
* Fixing local URLs for event receiver by @JoshGrittnerTR in [#71](https://github.com/tr/ras-search_ai-conversations/pull/71)
* Keycite citing case ids by @JoshGrittnerTR in [#64](https://github.com/tr/ras-search_ai-conversations/pull/64)
* AB#1865596-  Adding topologySpreadConstraints by @satyadashTR in [#72](https://github.com/tr/ras-search_ai-conversations/pull/72)
* November Release by @scottberres-tr in [#73](https://github.com/tr/ras-search_ai-conversations/pull/73)
* updating dependencies by @scottberres-tr in [#74](https://github.com/tr/ras-search_ai-conversations/pull/74)
* adding keys to current root span by @scottberres-tr in [#75](https://github.com/tr/ras-search_ai-conversations/pull/75)
* Updated shared utils for ConversationEntryResult object fix by @RaviKadiwalaTR in [#76](https://github.com/tr/ras-search_ai-conversations/pull/76)
* fixing event issue by @scottberres-tr in [#77](https://github.com/tr/ras-search_ai-conversations/pull/77)
* added x_tr_user_classification header by @scottberres-tr in [#79](https://github.com/tr/ras-search_ai-conversations/pull/79)
* Update conversation_router.py by @scottberres-tr in [#80](https://github.com/tr/ras-search_ai-conversations/pull/80)
* Updated by @scottberres-tr in [#81](https://github.com/tr/ras-search_ai-conversations/pull/81)
* 16 pods by @scottberres-tr in [#82](https://github.com/tr/ras-search_ai-conversations/pull/82)
* Update poetry.lock by @JohnEngelhart in [#83](https://github.com/tr/ras-search_ai-conversations/pull/83)
* fixing serialization issues by @JohnEngelhart in [#84](https://github.com/tr/ras-search_ai-conversations/pull/84)
* Update poetry.lock by @JohnEngelhart in [#86](https://github.com/tr/ras-search_ai-conversations/pull/86)
* fixing serialization error by @scottberres-tr in [#87](https://github.com/tr/ras-search_ai-conversations/pull/87)
* Upgrading python-utils for new PL wheel by @ChristianEggersTR in [#89](https://github.com/tr/ras-search_ai-conversations/pull/89)
* Upgrade dependencies for ai-rag-practical-law by @ChristianEggersTR in [#91](https://github.com/tr/ras-search_ai-conversations/pull/91)
* Us 1869295 v2 task by @satyadashTR in [#94](https://github.com/tr/ras-search_ai-conversations/pull/94)
* AB#1869295 V2 start or continue conversation impl continues by @satyadashTR in [#95](https://github.com/tr/ras-search_ai-conversations/pull/95)
* V2 dev by @JoshGrittnerTR in [#93](https://github.com/tr/ras-search_ai-conversations/pull/93)
* update to testspec to run different command for prod vs other envs, u… by @AdamPetersonTR in [#92](https://github.com/tr/ras-search_ai-conversations/pull/92)
* Adding DD conversation_type to span by @JoshGrittnerTR in [#90](https://github.com/tr/ras-search_ai-conversations/pull/90)
* December Release Candidate by @scottberres-tr in [#85](https://github.com/tr/ras-search_ai-conversations/pull/85)
* Dev by @scottberres-tr in [#105](https://github.com/tr/ras-search_ai-conversations/pull/105)
* Dev by @scottberres-tr in [#106](https://github.com/tr/ras-search_ai-conversations/pull/106)

### New Contributors

* @AdamPetersonTR made their first contribution in [#2](https://github.com/tr/ras-search_ai-conversations/pull/2)
* @RaviKadiwalaTR made their first contribution in [#7](https://github.com/tr/ras-search_ai-conversations/pull/7)
* @HarshitSrivastavaTR made their first contribution in [#9](https://github.com/tr/ras-search_ai-conversations/pull/9)
* @JohnEngelhart made their first contribution in [#13](https://github.com/tr/ras-search_ai-conversations/pull/13)
* @scottberres-tr made their first contribution in [#40](https://github.com/tr/ras-search_ai-conversations/pull/40)
* @LathaKalathuruTR made their first contribution in [#36](https://github.com/tr/ras-search_ai-conversations/pull/36)
* @KevinHuberTR made their first contribution in [#66](https://github.com/tr/ras-search_ai-conversations/pull/66)
* @ChristianEggersTR made their first contribution in [#89](https://github.com/tr/ras-search_ai-conversations/pull/89)


