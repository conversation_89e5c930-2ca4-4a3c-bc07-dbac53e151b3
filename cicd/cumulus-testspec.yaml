version: 0.2

env:
  shell: bash
  variables:
    JFROG_USERNAME: 's.ras-search'
  secrets-manager:
    JFRO<PERSON>_PASSWORD: 'arn:aws:secretsmanager:us-east-1:235790704225:secret:a207891/ras/search/artifactory/access-token-Np6tCT'

phases:
  install:
    runtime-versions:
      python: 3.11
      nodejs: latest

  pre_build:
    commands:
      - echo "install pip dependencies, allure dependencies, and ai-conversations testing module"
      - export PIP_EXTRA_INDEX_URL="https://${JFROG_USERNAME}:${JFROG_PASSWORD}@tr1.jfrog.io/artifactory/api/pypi/pypi/simple"
      - pip install --no-cache --upgrade pip
      - mkdir -p /usr/share/keyrings
      - chmod 755 /usr/share/keyrings
      - curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg -o /usr/share/keyrings/yarnpkg-archive-keyring.gpg
      - mkdir -p /etc/apt/sources.list.d
      - echo "deb [signed-by=/usr/share/keyrings/yarnpkg-archive-keyring.gpg] https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list
      - rm -f /etc/apt/sources.list.d/sbt.list
      - curl -o allure-2.13.8.zip -Ls https://github.com/allure-framework/allure2/releases/download/2.13.8/allure-2.13.8.zip
      - unzip allure-2.13.8.zip
      - export PATH=$PATH:./allure-2.13.8/bin/
      - pip install --upgrade awscli
      - allure --version
      - pwd
      - ls -ltr
      - uname -a
      - DATE=`date "+%Y%m%d%H%M%S"`
      - echo "installing ai-conversations testing module now"
      - pip install ras-shared-python-ai-conversations-qa-testing

  build:
    on-failure: ABORT
    commands:
      - echo "printing out basic information for this codebuild to help debug and develop in the future"
      - ps -p $$
      - printenv
      - echo "listing out pwd and contents inside"
      - pwd
      - ls -ltr
      - echo "changing dir to root"
      - cd /
      - echo "printing out pwd and contents inside"
      - pwd
      - ls -ltr
      - echo "changing dir to python site packages folder where our tests module can be found"
      - cd /root/.pyenv/versions/${PYTHON_311_VERSION}/lib/python3.11/site-packages
      - echo "printing out pwd and contents inside"
      - pwd
      - ls -ltr
#      - echo "Running Post Deploy Tests"
#      - pytest -v -s -m "${PYTEST_MARK}" ai_conversations_qa_testing --dns ${APPLICATION_DNS} --env ${ENV} --svc ai_conversations_automated_testing --stdout false

      - echo "As of now, only prod, qa, int, and ci envs are handled for executing post deploy tests."
      - echo "Any other env passed in will not trigger post deploy tests when this codebuild is called."
#      - allure generate ai_conversations_qa_testing/allure-report --clean -o reports
#      - pwd
#      - ls -ltr
#      - zip -r QA-Test-Report.zip reports

  post_build:
    commands:
      - echo "qa testing tests execution completed"
