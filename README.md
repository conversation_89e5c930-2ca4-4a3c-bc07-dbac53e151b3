<!-- START badge-template.html --><svg fill="none" viewBox="0 0 120 120" width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <foreignObject width="100%" height="100%">
    <div xmlns="http://www.w3.org/1999/xhtml">
      <a href="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/tr/ras-search_ai-conversations/badges/http-core-update/last-badge-update.svg" target="_blank">
        <img alt="Last Updated" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/http-core-update/last-badge-update.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-conversations/actions/runs/15707374248" target="_blank">
        <img alt="CI Build" src="https://github.com/tr/ras-search_ai-conversations/actions/workflows/python-build.yml/badge.svg?branch=http-core-update">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-conversations/pulls?q=is:pr+created:%3C%3D2025-06-10+is%3Aopen" target="_blank">
      <img alt="Stale Pull Requests" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/http-core-update/stale-pr-count.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-conversations/labels/dependencies?q=+is%3Aopen" target="_blank">
      <img alt="Dated Dependencies" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/http-core-update/dated-dependency-count.svg">
      </a>
      <br />
      <a href="" target="_blank">
        <img alt="Code Coverage" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/http-core-update/code-coverage.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-conversations/runs/44256482955" target="_blank">
        <img alt="Lines of Code" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/http-core-update/lines-of-code.svg">
      </a>
      <br />
      <a href="https://github.com/tr/ras-search_ai-conversations/releases/latest" target="_blank">
      <img alt="Latest Release" src="https://github.com/tr/ras-search_ai-conversations/blob/tr-cicd-resources/badges/main/latest-release.svg">
      </a>
    </div>
  </foreignObject>
</svg>

<!-- END badge-template.html -->
# ras-search_ai-conversations
 ai conversation endpoints

To run locally

Set the following env vars in the run config.

    PYTHONUNBUFFERED=1;
    DD_ENV=local;
    DD_PROFILING_ENABLED=false;
    DD_SERVICE=ai-conversations;
    DD_TRACE_ENABLED=True;
    DD_VERSION=0.0.1;
    RESOURCES_DIR=./app/config/resources
    HOSTNAME=[Your workstation name]

The working directory needs to be set to the root not the /app folder.


## To run locally with docker
AI conversations now relies on Redis, DynamoDb, and DD agent to be run locally via docker desktop.

1. Install docker desktop.
2. After loading docker desktop run the following to pull the right images.
    ```
    docker pull redis:latest
    docker pull amazon/dynamodb-local
    docker pull datadog/agent:latest
    ```

3. Run the following to start the containers.
    ```
   docker run -d -p 6379:6379 --name redis redis
   docker run -d -p 8000:8000 --name dynamodb amazon/dynamodb-local
   docker run -d -p 8125:8125 -p 8126:8126  -e DD_HOSTNAME=local.developer -e DD_ENV=local -e DD_API_KEY=[get this from secret manager] --name datadog_agent datadog/agent:latest

4. Cloud-tool into the pre-prod ras search account.
   ```
   cloud-tool login -u 'mgmt\your_user_id' -p your_password
   tr-ras-search-preprod (653661534649): human-role/a207891-PowerUser2 (RAS Search)

5. You are now able to launch the ai-conversations, workers, and monitor locally.

**To have a better experience, you will need to install postman.**
**You will also need a pre-prod gcs token**

**IDE may require to mark 'app' directory as source root in order to compile imports correctly**

## Postman Setup
   ```
   https://trten-my.sharepoint.com/:u:/g/personal/scott_berres_thomsonreuters_com/Ea6o-kZKNXNLqrSwpldaqwYBYxedQ1kQamIRAZvd2WFQAg?e=b3EW7q
   https://trten-my.sharepoint.com/:u:/g/personal/scott_berres_thomsonreuters_com/EeiM7pRnFi5Mh3XdfJKTgOgBbxBIgbm03kD4dwcsi-i5mQ?e=Pdt4WL
   https://trten-my.sharepoint.com/:u:/g/personal/scott_berres_thomsonreuters_com/EVexxQqFuytMs3mwNhOl9X8BiDtykJLURWWoFuPeE1LN7A?e=JlIhar
   ```

## Pre-Commit Steps
1. You must run the following prior to committing code. This will run some pre-commit checks, format the code and run any tests available.
   From a terminal window run the following.
   ```
   poe test
   ```
## Runnint Unit Tests Locally
- Running unit tests has been problematic in the past.  Here are a few tips to help run this locally
  -  within pytest.ini, there is a note regarding Intellij at about like 22
     (appears to affect pycharm also), this 'may' help get your IDE to stop at the breakpoint
- Unfortunately, have not figured out how to run individual tests and get them to execute properly.  Here is the suggestion:
  - create a 'module' 'Python Test' run configuration in your IDE.
  - set the 'module' name to 'tests'
  - Python parameters should be: --no-cov
  - Additional parameters should be" --no-cov
  - Working directory is {your directory path}\ras-search_ai-conversations
- Once you run this, you should be able to 'debug' your specific test.  If it fails, check the run configuration
  being executed for the working directory.
- Please update this section if you find a better way to run the tests.

## Running Smoke Tests Locally While LOCALHOST is running
NOTE: you need to be cloud-tool'ed into the ras-search-preprod account so you have access to the secret which allows us to auth into gcs

- Run the following command to make sure you have the latest testing module installed in your venv
  - `poetry update`
- Then cd into the .venv\lib\site-packages folder since we install the testing module as a wheel file
  - `cd .\.venv\Lib\site-packages\`
- Then execute the pytest command to execute smoke tests on the localhost dns
  - `pytest -v -s -m smoke .\ai_conversations_qa_testing\ --dns http://localhost:8010 --env local`