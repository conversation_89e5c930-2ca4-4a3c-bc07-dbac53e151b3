import json
import os
from functools import lru_cache

from configuration_utils.configuration_core import CoreSettings, replace_region_values_in_settings
from raslogger.logging_factory import LoggingFactory

logger = LoggingFactory.get_logger(__name__)


class Settings(CoreSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Research Application Services (RAS) AI Conversations"
    GCS_ENVIRONMENT: str
    APP_DOMAIN: str
    CLUSTER_SHORT: str

    SAGEMAKER_EMBEDDING_ENDPOINT: str
    SAGEMAKER_PL_EMBEDDING_ENDPOINT: str
    OPEN_SEARCH_ENDPOINT: str
    OPEN_SEARCH_SECRET_ID: str

    DYNAMO_TABLE_NAME: str
    LOGGER_FIX_LIST: str
    S3_BUCKET: str
    DYNAMODB_TTL: str
    TEMP_FOLDER: str
    UDS_ENDPOINT: str
    DEFAULT_PROFILE_MAPPING_SECRET: str
    RAS_CONFIG_BASE_URL: str
    SNAPSHOT_TABLE_NAME: str


@lru_cache()
def get_settings():
    logger.info("current working directory: " + os.getcwd())
    logger.info("getting settings from file: " + ".env." + os.getenv("ENVIRONMENT", "local"))
    settings = Settings()
    updated_settings = replace_region_values_in_settings(settings)
    logger.info("settings are: " + json.dumps(updated_settings.dict()))
    return updated_settings
