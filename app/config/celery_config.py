from functools import lru_cache

from celery import current_app as current_celery_app
from conversation_core.celery_config.base_config import CeleryBaseSettings
from conversation_core.celery_config.v2.queue_config import QueueConfig

from services.chat_profile_service import answer_profile_service
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)


@lru_cache()
def get_celery_settings():
    settings = CeleryBaseSettings()
    return settings


def create_celery_app():
    input_settings = get_celery_settings()
    celery_app = current_celery_app
    celery_app.conf.update(broker_url=input_settings.broker_url)
    celery_app.conf.update(result_backend=input_settings.result_backend)
    celery_app.conf.update(broker_connection_retry_on_startup=True)
    celery_app.conf.update(broker_pool_limit=100)
    celery_app.conf.update(task_track_started=True)
    celery_app.conf.update(task_queue_max_priority=10)
    celery_app.conf.update(task_default_priority=5)
    celery_app.conf.update(task_serializer="pickle")
    celery_app.conf.update(result_serializer="pickle")
    celery_app.conf.update(accept_content=["pickle", "json"])
    celery_app.conf.update(result_persistent=True)
    celery_app.conf.update(result_expires=3600)
    celery_app.conf.update(task_queues=answer_profile_service.get_all_queues_v2())
    celery_app.conf.update(task_routes=(QueueConfiguration().route_task,))
    # Explicitly setting visibility_timeout to 60 minutes
    # All celery applications on the same Redis instance adopt the lowest value among all celery applications 
    # Do not change these values or you may impact other applications
    celery_app.conf.update(visibility_timeout=60 * 60)
    celery_app.conf.update(result_backend_transport_options={'visibility_timeout': 60 * 60})
    celery_app.conf.update(broker_transport_options={'visibility_timeout': 60 * 60})
    celery_app.conf.update()
    return celery_app


class QueueConfiguration(QueueConfig):
    def __init__(self):
        super().__init__(answer_profile_service)
