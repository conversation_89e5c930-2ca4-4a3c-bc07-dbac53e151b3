ENVIRONMENT=local
GCS_ENVIRONMENT=qa
DYNAMO_TABLE_NAME=a207891-ai-acceleration-conversations-ci
SNAPSHOT_TABLE_NAME=a207891-conversation-snapshots-ci
LOGGER_FIX_LIST="gunicorn,uvicorn,langchain.chat_models.openai,ddtrace.profiling,azure,celery,asyncio"
S3_BUCKET=a207891-ai-acceleration-conversations-ci-use1
EVENT_RECEIVER_ENDPOINT=https://event-receiver-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com/events/v2/event
DYNAMODB_TTL=7
DATADOG_API_SECRET_NAME=a207891-datadog-api-key-use1
DATADOG_METRIC_PREFIX=ras.search.conversations
OVERRIDE_HOST_NAME=local_developer
DATADOG_METRICS_ENABLED=True
EVENTS_ENABLED=True
TEMP_FOLDER=/temp
SAGEMAKER_EMBEDDING_ENDPOINT=a207891-ai-acceleration-coco-qa-use1
SAGEMAKER_PL_EMBEDDING_ENDPOINT=a207891-ai-acceleration-pl-search-mod-qa-use1
OPEN_SEARCH_ENDPOINT=https://ai-accelerate-open-search-qa-use1.4649.aws-int.thomsonreuters.com
OPEN_SEARCH_SECRET_ID=a207891-ai-accel-qa-use1-masterUserPassphrase
UDS_ENDPOINT=http://uds.int.next.qed.westlaw.com
DEFAULT_PROFILE_MAPPING_SECRET=a207891/ras-search/ai-acceleration/ci/profile-override-mappings
APP_DOMAIN=https://ai-conversations-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
RAS_CONFIG_BASE_URL=https://ras-configuration-py-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
GCS_URL=https://entitlement-qa.gcs.int.thomsonreuters.com/v1/token
GCS_USER_SECRET=a207891/ras-search/ai-acceleration/qa/gcs-client-worker-secret
CLUSTER_SHORT=ras2