from fastapi import Request
from raslogger import config as logger_config
from configuration_utils.constants import Constants as ConfigConstants
from api.models.conversations.user_session_model import UserSession
from app.config.settings import Settings
from utils.span_utils import add_user_classification_to_span


def set_user_session(request: Request, settings: Settings) -> UserSession:
    user_session = UserSession(request)
    user_session_info = user_session.get_session_info()

    # Setting Logging Sensitivity
    if ConfigConstants.SESSION_USER_SENSITIVITY in user_session.get_session_info():
        user_session_info = user_session.get_session_info()
        if user_session_info[ConfigConstants.SESSION_USER_SENSITIVITY] == ConfigConstants.SESSION_BLIND_SENSITIVITY:
            logger_config.logging_type = logger_config.BLIND_LOGGING
        else:
            logger_config.logging_type = logger_config.STANDARD_LOGGING

    # Set Classification
    add_user_classification_to_span(user_session_info)
    return user_session
