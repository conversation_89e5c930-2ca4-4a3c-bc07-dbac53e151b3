from configuration_utils.constants import Constants as ConfigConstants
from conversation_core.shared.utils.trace import add_to_current_span
from fastapi import HTTPException



def set_span_http_code(code: int):
    add_to_current_span("http.status_code", str(code))


def raise_http_error(code: int, detail: str):
    set_span_http_code(code)
    raise HTTPException(status_code=code, detail=detail)


def add_user_classification_to_span(user_session: dict):  # pragma: no cover
    user_class = user_session.get(ConfigConstants.SESSION_USER_CLASSIFICATION, "unknown")
    add_to_current_span("user_classification", user_class)
    return user_class
