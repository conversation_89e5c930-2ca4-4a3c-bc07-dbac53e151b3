from raslogger.logging_factory import LoggingFactory

logger = LoggingFactory.get_logger(__name__)

"""String utility functions."""


def snake_to_camel_case(string: str) -> str:
    """Converts a string to lower camelCase."""
    if string is None:
        error_msg = f"Invalid string argument '{string}'."
        logger.error(f"string::{snake_to_camel_case.__name__}:: {error_msg}")
        raise TypeError(error_msg)

    words = string.split("_")
    return words[0].lower() + "".join(word.capitalize() for word in words[1:])
