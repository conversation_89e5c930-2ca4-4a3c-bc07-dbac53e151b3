import json
import boto3


def call_sagemaker_endpoint(endpoint_name, region_name, input_payload, role_arn=None):
    if role_arn is not None:
        # Assume the role to obtain temporary credentials
        sts_client = boto3.client("sts")
        response = sts_client.assume_role(RoleArn=role_arn, RoleSessionName="sagemaker_session")
        credentials = response["Credentials"]
        client = boto3.client(
            "sagemaker-runtime",
            region_name=region_name,
            aws_access_key_id=credentials["AccessKeyId"],
            aws_secret_access_key=credentials["SecretAccessKey"],
            aws_session_token=credentials["SessionToken"],
        )
    else:
        client = boto3.client("sagemaker-runtime", region_name=region_name)

    # Define the input data
    # input_data = {"text": query}

    # Convert input data to JSON string
    payload = json.dumps(input_payload)

    # Invoke the endpoint
    response = client.invoke_endpoint(EndpointName=endpoint_name, Body=payload, ContentType="application/json")

    # Get the output from the response
    output = json.loads(response["Body"].read().decode())

    return output


if __name__ == "__main__":
    print(
        call_sagemaker_endpoint(
            "a207891-ai-acceleration-coco-ci-use1",
            "us-east-1",
            '{"passageId": "stub", "passageText": "slip and fall" }',
        )
    )
