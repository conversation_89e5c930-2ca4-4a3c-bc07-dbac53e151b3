import time
from typing import List, Optional

from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2, generate_conversation_sort_key
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException
from conversation_core.shared.enums import ConversationActionType, RetrieveConversationEntryStatuses, \
    UserTypeHeaderValues
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.v2.conversation import ConversationMetadata as ConversationMetadataV2, Conversation
from conversation_core.shared.models.v2.conversation import StartConversationRequest
from conversation_core.shared.models.v2.conversation_entry import \
    ConversationEntryMetadata as ConversationEntryMetadataV2, ConversationEntry, EmailNotification
from conversation_core.shared.models.v3.conversation import StartConversationRequestV3
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from conversation_core.shared.utils.trace import add_to_current_span
from conversation_core.shared.enums import AalpSkill
from fastapi import Head<PERSON>
from raslogger import LoggingFactory
from starlette import status
from starlette.exceptions import HTTPException

from api.models.detector_models import DetectorRequest
from app.api.exceptions.conversation_exceptions import ConversationNotFoundException, \
    InvalidConversationOverrideException, \
    InvalidConversationFilterException, ConversationMaxHoursExceededException, ConversationMaxEntriesExceededException, \
    ConversationInProgressEntryException, InvalidConversationSkillException
from config import settings
from config.settings import Settings
from services.chat_profile_service import answer_profile_service

logger = LoggingFactory.get_logger(__name__)

settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)


def get_original_profile(conversation_entries: List[ConversationEntry]) -> Optional[str]:
    conversation_entries.sort(key=lambda x: x.timestamp)
    if conversation_entries[0].result and conversation_entries[0].result.answer_solution_profile:
        return conversation_entries[0].result.answer_solution_profile
    return None


async def check_product_name_header(
        x_tr_product_name: Optional[str] = Header(
            default=None,
            description="New header for product name",
            example="SwaggerUI"),
        x_trmr_product: Optional[str] = Header(
            default=None,
            description="Legacy Cobalt header for product name",
            example="SwaggerUI")
):
    x_tr_product_name = check_header_validity(x_tr_product_name,"x_tr_product_name")
    x_trmr_product = check_header_validity(x_trmr_product,"x_trmr_product")
    if not x_tr_product_name and not x_trmr_product:
        raise HTTPException(
            status_code=400,
            detail="Either 'x-tr-product-name' or 'x-trmr-product' header must be included."
        )
    return x_tr_product_name or x_trmr_product


async def check_asset_id_header(
        x_tr_asset_id: Optional[str] = Header(
            default=None,
            description="Header for asset id to associate this conversation with",
            example="207891"),
):
    if x_tr_asset_id:
        if x_tr_asset_id.isdigit():
            add_to_current_span("asset_id", x_tr_asset_id)
        else:
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message="Asset ID must be a number.")
    else:
        logger.warn("No Asset ID provided, logging warning as it is not required for now")


async def check_user_type_header(
        x_tr_user_type: Optional[UserTypeHeaderValues] = Header(
            default=None,
            description="Header for user type to associate this conversation with",
            example="GOVERNMENT"),
):
    user_type_value = UserTypeHeaderValues.STANDARD.value
    if x_tr_user_type:
        user_type_value = x_tr_user_type.value
    else:
        logger.info("No user type provided, assuming standard user")
    add_to_current_span("user_type", user_type_value)
    return user_type_value


async def check_product_view_header(
        x_tr_product_view: Optional[str] = Header(
            default=None,
            description="New header for product view",
            example="Docs"),
        x_cobalt_security_productview: Optional[str] = Header(
            default=None,
            description="Legacy Cobalt header for product view",
            example="Docs")
):
    x_tr_product_view = check_header_validity(x_tr_product_view,"x_tr_product_view")
    x_cobalt_security_productview = check_header_validity(x_cobalt_security_productview,"x_cobalt_security_productview")
    if not x_tr_product_view and not x_cobalt_security_productview:
        raise HTTPException(
            status_code=400,
            detail="Either 'x-tr-product-view' or 'x-cobalt-security-productview' header must be included."
        )
    return x_tr_product_view or x_cobalt_security_productview


async def validate_conversation_request(start_conversation_request: StartConversationRequest):
    try:
        answer_profile = answer_profile_service.get_profile(
            name=start_conversation_request.answer_solution_profile.lower())
        bypass_content_type_check = getattr(answer_profile, 'bypass_content_type_check', False)

        if start_conversation_request.conversation_entry_metadata:
            await validate_conversation_entry_metadata(
                conversation_entry_metadata=start_conversation_request.conversation_entry_metadata
            )

        if start_conversation_request.content_types_override and not bypass_content_type_check:
            allowed_content_types = answer_profile.default_content_types
            if not set(start_conversation_request.content_types_override).issubset(allowed_content_types):
                await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                          message="Content type is not allowed for "
                                                  f"{start_conversation_request.answer_solution_profile}")
            if (start_conversation_request.content_types_exclude is not None
                    and len(start_conversation_request.content_types_exclude) > 0):
                await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                          message="Requests cannot have both a content type override and exclude")
        if len(start_conversation_request.user_input.strip(" ")) == 0:
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message="User input cannot be empty")
        return await validate_custom_field(start_conversation_request)
    except ProfileNotFoundException as e:
        await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message=str(e))

    if len(start_conversation_request.user_input.strip(" ")) == 0:
        await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message="User input cannot be empty")

    if start_conversation_request.content_types_override and not bypass_content_type_check:
        allowed_content_types = answer_profile.supported_answer_content_types
        if not set(start_conversation_request.content_types_override).issubset(allowed_content_types):
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                      message=f"Content type is not allowed for {start_conversation_request.answer_solution_profile}")

    return await validate_custom_field(start_conversation_request)


async def validate_conversation_v3_request(start_conversation_request: StartConversationRequestV3):
    answer_profile = answer_profile_service.get_profile(
        name=start_conversation_request.answer_solution_profile.lower())
    if start_conversation_request.overrides:
        for override in start_conversation_request.overrides:
            if override not in Constants.VALID_OVERRIDES:
                raise InvalidConversationOverrideException(override)
    if start_conversation_request.filters:
        for conversation_filter in start_conversation_request.filters:
            if conversation_filter not in Constants.VALID_FILTERS:
                raise InvalidConversationFilterException(conversation_filter)

    if len(start_conversation_request.user_input.strip(" ")) == 0:
        await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message="User input cannot be empty")

    if start_conversation_request.subscribed_skills is None:
        raise InvalidConversationSkillException("No skills passed in request! "
                                                "Field subscribed_skills is a required field! "
                                                "Supply a valid list of skills, or an empty list if no skill routing "
                                                "is required")

    if start_conversation_request.conversation_entry_metadata:
        await validate_conversation_entry_metadata(
            conversation_entry_metadata=start_conversation_request.conversation_entry_metadata
        )

    return start_conversation_request


async def validate_conversation_entry_metadata(conversation_entry_metadata: ConversationEntryMetadataV2):
    if conversation_entry_metadata and conversation_entry_metadata.email_notification:
        email_notification: Optional[EmailNotification] = conversation_entry_metadata.email_notification
        if email_notification.email_sent is not None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                detail="conversation_entry_metadata.email_notification.email_sent field "
                                       "cannot be set by the client.")
        if email_notification.send_email:
            if email_notification.email_address is None or email_notification.email_address == "" or email_notification.email_address.casefold() == "none".casefold() or email_notification.email_address.casefold() == "null".casefold():
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                    detail="Send_email marked true, but no email address provided.")
            if email_notification.destination_url is None or email_notification.destination_url == "" or email_notification.destination_url.casefold() == "none".casefold() or email_notification.destination_url.casefold() == "null".casefold():
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                    detail="Send_email marked true, but no destination url provided.")
        return conversation_entry_metadata


async def validate_custom_field(start_conversation_request: StartConversationRequest):
    answer_profile = answer_profile_service.get_profile(name=start_conversation_request.answer_solution_profile.lower())
    conversation_custom = start_conversation_request.conversation_metadata.custom if start_conversation_request.conversation_metadata else None
    conversation_entry_custom = start_conversation_request.conversation_entry_metadata.custom if start_conversation_request.conversation_entry_metadata else None
    if start_conversation_request.answer_solution_profile and (conversation_custom or conversation_entry_custom):
        custom_metadata = answer_profile.allowed_meta_data_fields
        is_valid_conv_meta = set(conversation_custom.model_dump(exclude_none=True).keys()).issubset(
            custom_metadata.get('allowed_conv_fields', {})) if conversation_custom else True
        is_valid_conv_entry_meta = set(conversation_entry_custom.model_dump(exclude_none=True).keys()).issubset(
            custom_metadata.get('allowed_conv_entry_field', {})) if conversation_entry_custom else True
        if is_valid_conv_meta and is_valid_conv_entry_meta:
            return start_conversation_request
        else:
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                      message=f"Custom field is not allowed for {start_conversation_request.answer_solution_profile}")
    else:
        return start_conversation_request


async def validate_custom_field_in_metadata(user_id: str, conversation_id: str,
                                            conversation_metadata: ConversationMetadataV2):
    conversation_custom = conversation_metadata.custom
    if conversation_custom:
        answer_solution_profile = await get_answer_solution_profile(user_id, conversation_id)
        allowed_conv_set = answer_profile_service.get_profile(
            name=answer_solution_profile.lower()).allowed_meta_data_fields.get(
            'allowed_conv_fields', {})
        is_valid_conv_meta = set(conversation_custom.model_dump(exclude_none=True).keys()).issubset(allowed_conv_set)
        if is_valid_conv_meta:
            return conversation_metadata
        else:
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                      message=f"Custom field is not allowed for {answer_solution_profile}")
    else:
        return conversation_metadata


async def validate_custom_field_in_entry_metadata(user_id: str, conversation_id: str,
                                                  conversation_entry_id: str,
                                                  conversation_entry_metadata: ConversationEntryMetadataV2):
    conversation_entry_custom = conversation_entry_metadata.custom
    if conversation_entry_custom:
        answer_solution_profile = await get_answer_solution_profile(user_id, conversation_id)
        allowed_conv_entry_set = answer_profile_service.get_profile(
            name=answer_solution_profile.lower()).allowed_meta_data_fields.get(
            'allowed_conv_entry_field', {})
        is_valid_conv_entry_meta = set(conversation_entry_custom.model_dump(exclude_none=True).keys()).issubset(
            allowed_conv_entry_set)
        if is_valid_conv_entry_meta:
            return conversation_entry_metadata
        else:
            await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                      message=f"Custom field is not allowed for {answer_solution_profile}")
    else:
        return conversation_entry_metadata


async def validate_qcbs_detector(qcbs_detector_service_request: DetectorRequest):
    return qcbs_detector_service_request


async def log_and_raise_error(status_code: int, message: str):
    logger.error(message)
    raise HTTPException(status_code=status_code, detail=message)


async def get_answer_solution_profile(user_id, conversation_id):
    conversation_meta_data = None
    try:
        conversation_meta_data = dynamo_db_v2.retrieve_item(partition_key=user_id,
                                                            sort_key=generate_conversation_sort_key(conversation_id))
    except IndexError as e:
        logger.error(f"Index error:: {e}")
        await log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST, message='Input is invalid.')
    except DynamoDBNotFoundException as e:
        logger.error(f"DynamoDB not found exception:: {e}")
        await log_and_raise_error(status_code=status.HTTP_404_NOT_FOUND, message=f"No conversation details found for "
                                                                                 f"user_id : {user_id} and  "
                                                                                 f"conversation_id : {conversation_id}")
    except Exception as e:
        logger.error(f"Exception:: {e}")
        await log_and_raise_error(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                  message=f'Some unknown error occurred for user id: {user_id} '
                                          f'and conversation id: {conversation_id}')

    answer_solution_profile = conversation_meta_data.get('profile', None)
    if answer_solution_profile:
        return answer_solution_profile
    else:
        await log_and_raise_error(status_code=status.HTTP_404_NOT_FOUND,
                                  message=f"For the provided conversation id, the profile information doesn't exist.")


def is_in_progress_entry_status(entry_status: RetrieveConversationEntryStatuses) -> bool:
    return entry_status in [RetrieveConversationEntryStatuses.IN_PROGRESS,
                            RetrieveConversationEntryStatuses.PENDING,
                            RetrieveConversationEntryStatuses.QUEUED]


def validate_if_previous_conv_in_progress(conversation: Conversation, from_v3_endpoint: bool = False):
    if conversation.conversation_entries:
        in_progress_conv_entry: Optional[ConversationEntry] = (
            next((item for item in conversation.conversation_entries
                  if is_in_progress_entry_status(item.status)), None))
        if in_progress_conv_entry:
            # FIXME: This logic was commented out rather than deleted entirely because the exact impact of removing this
            # logic was not fully known; if there are no adverse effects noticed, this can be removed later

            # time_diff_in_seconds = int(time.time()) - in_progress_conv_entry.timestamp
            # latest_conv_entry_time_diff = time_diff_in_seconds / 60
            # logger.warn(
            #     f"Conversation has an in-progress entry. A follow up has been submitted after {latest_conv_entry_time_diff} minutes.")
            # if latest_conv_entry_time_diff < Constants.FOLLOWUP_SUBMIT_DELAY_MINUTES:
            raise ConversationInProgressEntryException()

    # Might not have any usable conversation entries yet, so try and use the conversation metadata values
    if (conversation.conversation_metadata
            and is_in_progress_entry_status(conversation.conversation_metadata.latest_entry_status)):
        # FIXME: This logic was commented out rather than deleted entirely because the exact impact of removing this
        # logic was not fully known; if there are no adverse effects noticed, this can be removed later

        # time_diff_in_seconds = int(time.time()) - conversation.conversation_metadata.latest_entry_date
        # latest_conv_entry_time_diff = time_diff_in_seconds / 60
        # logger.warn(
        #     f"Conversation has an in-progress entry. A follow up has been submitted after {latest_conv_entry_time_diff} minutes.")
        # if latest_conv_entry_time_diff < Constants.FOLLOWUP_SUBMIT_DELAY_MINUTES:
        raise ConversationInProgressEntryException()


def validate_conversation_for_followup(conversation: Conversation, answer_profile: AnswerProfile,
                                       from_v3_endpoint: bool = False):
    # Check if we found the existing Conversation
    if conversation is None:
        raise ConversationNotFoundException()

    # Check if too much time has passed since the conversation was created
    if (conversation.conversation_metadata and conversation.conversation_metadata.max_followup_date
            and conversation.conversation_metadata.max_followup_date > 0):
        if time.time() > conversation.conversation_metadata.max_followup_date:
            raise ConversationMaxHoursExceededException(conversation.conversation_id)

    # Check if too many entries have been added to this conversation
    num_of_conv_entries = sum(1 for item in conversation.conversation_entries
                              if item.conversation_action_type == ConversationActionType.RAG)
    max_entries_per_conversation = getattr(answer_profile, Constants.PROFILE_MAX_ENTRIES_PER_CONVO, -1)
    if (max_entries_per_conversation != -1
            and num_of_conv_entries >= max_entries_per_conversation):
        raise ConversationMaxEntriesExceededException(conversation.conversation_id, answer_profile.name)

    # Check if the conversation has an in-progress entry
    validate_if_previous_conv_in_progress(conversation=conversation, from_v3_endpoint=from_v3_endpoint)


def check_header_validity(header: str,header_name: str):
    if header is not None:
        accepted_special_characters = ".-_ "
        if header.casefold() == 'null'.casefold():
            return None
        for x in header:
            if not x.isalnum() and x not in accepted_special_characters:
                raise HTTPException(
                    status_code=400,
                    detail=header_name + " contains special characters: " + header
                )

    return header
