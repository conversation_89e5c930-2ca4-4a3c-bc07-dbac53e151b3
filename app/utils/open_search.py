import json
from typing import Optional, List

import boto3
import requests
from app.utils.sagemaker import call_sagemaker_endpoint
from config import settings

config_settings = settings.get_settings()

content_alias_mapping = {
    "pl_us_passages_v1": {
        "sagemaker": {
            "passage_id": "paraid",
            "passage_text": "text",
            "passage_vector": "text",
            "sagemaker_endpoint": config_settings.SAGEMAKER_PL_EMBEDDING_ENDPOINT,
        },
    },
    "pl_dqa_passages_us_v1": {
        "sagemaker": {
            "passage_id": "paraid",
            "passage_text": "text",
            "passage_vector": "text",
            "sagemaker_endpoint": config_settings.SAGEMAKER_PL_EMBEDDING_ENDPOINT,
        },
    },
    "default": {
        "sagemaker": {
            "passage_id": "passageId",
            "passage_text": "passageText",
            "passage_vector": "passageVector",
            "sagemaker_endpoint": config_settings.SAGEMAKER_EMBEDDING_ENDPOINT,
        }
    },
}


def execute_similarity_query(
    passage_text: str,
    opensearch_url: str,
    alias_name: str,
    vector_field: str,
    open_search_secret_id: str,
    size: int = 4,
    k: int = 4,
    jurisdiction_override: Optional[List[str]] = None,
    included_fields: Optional[List[str]] = None,
    excluded_fields: Optional[List[str]] = None,
):
    region = "us-east-1"

    mappings = content_alias_mapping.get(alias_name, content_alias_mapping["default"])
    sagemaker_endpoint = mappings["sagemaker"]["sagemaker_endpoint"]

    # Build the input payload
    input_payload = {
        f"{mappings['sagemaker']['passage_id']}": "stub",
        f"{mappings['sagemaker']['passage_text']}": passage_text,
    }

    input_embeddings = call_sagemaker_endpoint(sagemaker_endpoint, region, input_payload)

    if included_fields is None:
        included_fields = []

    if excluded_fields is None:
        excluded_fields = []

    # Build the query payload
    if jurisdiction_override is None or len(jurisdiction_override) == 0:
        query = {
            "size": size,
            "query": {
                "knn": {
                    f"{vector_field}": {"vector": input_embeddings[mappings["sagemaker"]["passage_vector"]], "k": k}
                }
            },
            "_source": {"includes": included_fields, "excludes": excluded_fields},
        }
    else:
        query = {
            "size": size,
            f"query": {
                "script_score": {
                    "query": {"bool": {"filter": {"terms": {"fermi_juris.keyword": jurisdiction_override}}}},
                    # TODO: this needs to also be dependent on the alias_name. Some content like PL dont have fermi juris
                    "script": {
                        "source": "knn_score",
                        "lang": "knn",
                        "params": {
                            "field": f"{vector_field}",
                            "query_value": input_embeddings[mappings["sagemaker"]["passage_vector"]],
                            "space_type": "l2",
                        },
                    },
                }
            },
            "_source": {"includes": included_fields, "excludes": excluded_fields},
        }

    client = boto3.client("secretsmanager")
    secret = json.loads(client.get_secret_value(SecretId=open_search_secret_id)["SecretString"])
    auth_header = requests.auth.HTTPBasicAuth(secret["username"], secret["password"])

    response = requests.post(opensearch_url + "/" + alias_name + "/_search", json=query, auth=auth_header)
    # Return the query response
    return response.json()


if __name__ == "__main__":
    # Set up the OpenSearch URL and index name
    # sagemaker_url = "a207891-ai-acceleration-coco-qa-use1"
    # sagemaker_url = "a207891-ai-acceleration-pl-search-mod-qa-use1"
    opensearch_url = "https://ai-accelerate-open-search-qa-use1.4649.aws-int.thomsonreuters.com"
    alias_name = "cases_synopsis_paras_v2"
    # alias_name = "pl_us_passages_v1"
    vector_field = "embedding"
    # Set up the query text
    passage_text = "breach of contract"
    response = execute_similarity_query(
        passage_text=passage_text,
        opensearch_url=opensearch_url,
        alias_name=alias_name,
        vector_field=vector_field,
        open_search_secret_id="a207891-ai-accel-qa-use1-masterUserPassphrase",
    )
    print(response)

    # extract the `hits` field from the dictionary
    hits = response["hits"]["hits"]

    # create a list of dictionaries containing only the fields we want
    data = [{"passage_text": h["_source"]["passage_text"], "doc_guid": h["_source"]["doc_guid"]} for h in hits]

    # create a dictionary with the data and convert to json string
    result = {"data": data}
    json_string = json.dumps(result)
    print(json_string)
