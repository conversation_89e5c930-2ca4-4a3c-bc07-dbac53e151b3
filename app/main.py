import logging
from contextlib import asynccontextmanager

import uvicorn
from ddtrace import patch_all
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.exceptions import ExceptionMiddleware


from app.api.endpoints.ann_query_router import ann_query_router
from common_utils.logging_helper import init_logging
from fastapi import FastAPI, Depends
from fastapi.responses import RedirectResponse
from raslogger import LoggingFactory
from typing_extensions import Annotated


from api.endpoints.conversation_router import conversation_router, conversation_api_v1, conversation_router_v2, conversation_api_v2
from api.endpoints.conversation_router_v3 import conversation_router_v3
from api.endpoints.detector_router import detector_router
from api.endpoints.snapshot_router import snapshot_router
from api.endpoints.health_check_router import health_check_router
from api.endpoints.intent_resolver_router import intent_router
from api.endpoints.request_evaluator_router import request_evaluator_router, request_evaluator_router_v2
from api.endpoints.statistics_router import conversation_statistics_router, conversation_statistics_router_v2
from app.api.error_handlers import conversation_handlers, common_handlers
from app.config.settings import Settings
from config import settings
from config.celery_config import create_celery_app
from core.filters import EndpointFilter, DatadogStartupFilter

logger = LoggingFactory.get_logger(__name__)
patch_all()
description = """
RAS AI Conversations API provides the ability to interact with the various AI solutions provided by Thomson Reuters.

Users provide input and an answer solution profile to the API, which then creates a conversation and returns the new ID.

Conversations are supported with a Celery framework which create tasks and places them on priority queues for backend workers to pick up.

A standard workflow would be the following
 1. **Start** a new conversation with a user input and answer solution profile
 2. **Poll** a GET endpoint to get the newly created conversation entry status
 3. **Retrieve results** for the conversation entry
 4. **Continue** the conversation with a follow up query
 5. **Poll** a GET endpoint to get the newly created follow up conversation entry status
 6. **Retrieve results** for the follow up conversation entry

## Documentation
For more in depth documentation about the system, please refer to the [RAS AI Conversations Documentation](https://helix.thomsonreuters.com/static-sites/site-builds/ras_documentation/ras-documentation/02_ai_ecosystem/typical-workflow.html)

## Terminology
### Answer Solution Profile
Profiles defined through RAS Configuration API, which defines the actions and AI solutions to be used for the conversation. Profiles are managed through the [Content Console](https://contentconsole-qa.int.thomsonreuters.com/ras-configuration/ai-search-configuration/chat-profile-list)
### Conversation
Made up of one to many conversation entries, each entry is a single action or skill that is defined in the profile supplied
### Conversation Entry
A single action or skill. Example: Intent (identifying the intent behind the user input), RAG (generating an answer from a LLM), etc.
### Metadata
Metadata is used to store additional information about the conversation or conversation entry. Custom field allows product secific data to be stored (defined in profiles).

## Authorization
GCS token should be passed in the Authorization header for all endpoints. The token should be prefixed with 'Bearer ' and should have the right 'MANAGE_RAS_SEARCH_AI_CONVERSATION'.

## Environments (Updated By Adam)
 - DEV - https://ai-conversations-dev.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com
 - CI - https://ai-conversations-ci.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com
 - INT - https://ai-conversations-int.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com
 - QA - https://ai-conversations-qa.plexus-{cluster_short}-pp{region_short}.5771.aws-int.thomsonreuters.com
 - PROD - https://ai-conversations-prod.plexus-{cluster_short}-{region_short}.3772.aws-int.thomsonreuters.com
"""

tags_metadata = [
    {
        "name": "Upcoming",
        "description": "Upcoming endpoints that are not yet released and are in progress."
    },
    {
        "name": "Start or Continue Conversation",
        "description": "Latest endpoints to use for creating or continuing a conversation."
    },
    {
        "name": "Conversation Actions",
        "description": "Latest endpoints to use for updating, and retrieving conversation information."
    },
    {
        "name": "Request Evaluation",
        "description": "Endpoints to evaluate the request utilizing common AI solutions in a stateless synchronous manner."
    },
    {
        "name": "Non-Chat Conversation Actions",
        "description": "APIs for non-chat conversation actions that require more than a user input query"
    },
    {
        "name": "Metadata",
        "description": "APIs to manage Conversation and Conversation Entry metadata."
    },
    {
        "name": "Developer Endpoints",
        "description": "Convenience APIs that are used by developers."
    },
    {
        "name": "V3",
        "description": "Version 3 of endpoints"
    },
    {
        "name": "V2",
        "description": "Version 2 of endpoints"
    },
    {
        "name": "V1",
        "description": "Version 1 of endpoints"
    },
    {
        "name": "Deprecated",
        "description": "Old API endpoints, that should not be integrated with any longer."
    }
]


def fix_logging(input_settings: Annotated[Settings, Depends(settings.get_settings)]):
    init_logging(input_settings.LOGGER_FIX_LIST.split(","))
    logging.getLogger("uvicorn.access").addFilter(EndpointFilter())
    logging.getLogger("ddtrace.profiling.profiler").addFilter(DatadogStartupFilter())


def create_app(input_settings: Annotated[Settings, Depends(settings.get_settings)]) -> FastAPI:
    web_app = FastAPI(
        title="RAS AI Conversations",
        openapi_url=f"{input_settings.API_V1_STR}/openapi.json",
        description=description.replace("{cluster_short}", input_settings.CLUSTER_SHORT).replace("{region_short}", input_settings.REGION_SHORT),
        openapi_tags=tags_metadata,
        lifespan=lifespan
    )

    web_app.openapi_version = "3.0.0"
    origins = ["https://developerportal.thomsonreuters.com", "http://localhost:8010"]
    if input_settings.APP_DOMAIN:
        origins.append(input_settings.APP_DOMAIN)
    web_app.add_middleware(ExceptionMiddleware, handlers=web_app.exception_handlers)
    web_app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["POST", "PUT", "GET", "OPTIONS", "DELETE", "PATCH"],
        allow_headers=["Content-Type", "Accept", "Authorization",
                       "x-datadog-origin", "x-datadog-parent-id", "x-datadog-sampled",
                       "x-datadog-sampling-priority", "x-datadog-trace-id",
                        "Access-Control-Allow-Origin", "Access-Control-Allow-Methods",
                       "Access-Control-Allow-Headers", "x-tr-product-name", "x-tr-product-view"]
    )

    web_app.celery_app = create_celery_app()

    return web_app


settings = settings.get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("***************************** Starting application - on_event(startup). *****************************")
    logger.info("***************************** End Starting - on_event(startup). ******************************")
    yield
    logger.info("***************************** Ending application - on_event(shutdown). *****************************")

app = create_app(settings)
fix_logging(settings)
celery_app = app.celery_app

app.include_router(health_check_router)
app.include_router(snapshot_router)
app.include_router(conversation_router)
app.include_router(ann_query_router)
app.include_router(conversation_api_v1)
app.include_router(conversation_router_v2)
app.include_router(conversation_api_v2)
app.include_router(conversation_router_v3)
app.include_router(request_evaluator_router)
app.include_router(request_evaluator_router_v2)
app.include_router(intent_router)
app.include_router(conversation_statistics_router)
app.include_router(conversation_statistics_router_v2)
app.include_router(detector_router)

# exception handlers initialization
common_handlers.init_exception_handlers(app)
conversation_handlers.init_exception_handlers(app)


@app.get("/swagger-ui/index.html", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")


if __name__ == "__main__":
    logger.info("************ Starting application from __main__. *******************")
    uvicorn.run("app.main:app", host="0.0.0.0", port=8010, workers=1)
#     main file changes
