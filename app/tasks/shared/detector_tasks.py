import time
from typing import Optional, List

from celery import shared_task
from conversation_core.shared.enums import ConversationActionType
from conversation_core.shared.tasks.task_contracts import DetectorTasks
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)


class DetectorTask(DetectorTasks):
    @shared_task(bind=True,
                 autoretry_for=(Exception,),
                 retry_backoff=True,
                 retry_kwargs={"max_retries": 1},
                 name="qcbs_detector")
    def start_detector_task(
            self,
            user_id: str,
            brief_id: str,
            quotation_guids: List[str],
            answer_solution_profile: str,
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            cobalt_session: dict = None,
            meta_data: Optional[dict] = None
    ):
        logger.info("Task Queued!")


detector_task = DetectorTask()


def send_detector_task(
        user_id: str,
        brief_id: str,
        quotation_guids: List[str],
        answer_solution_profile: str,
        conversation_id: str,
        conversation_entry_id: str,
        conversation_action_type: ConversationActionType,
        auth_token: str,
        cobalt_session: dict = None,
        route_to_green: Optional[bool] = False,
        user_classification: Optional[str] = "unknown",
):
    task_id = conversation_entry_id

    task = detector_task.start_detector_task.apply_async(
        args=[
            user_id,
            brief_id,
            quotation_guids,
            answer_solution_profile,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            cobalt_session,
        ],
        kwargs=None,
        task_id=task_id,
        headers={"time_sent": time.time(), "conversation_id": conversation_id,
                 "green": route_to_green, "user_classification": user_classification},
    )
    logger.info("end create_conversation_entry with task_id=" + task.task_id)
