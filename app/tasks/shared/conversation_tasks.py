import time
import uuid
from typing import Optional, List

from celery import shared_task
from conversation_core.cobalt.shared_tasks import CobaltConversation
from conversation_core.shared.enums import ConversationActionType, AalpSkill
from conversation_core.shared.constants import Constants
from conversation_core.shared.tasks.task_contracts import ConversationTasks, IntentResolverTasks, \
    CommonIRTasks
from conversation_core.shared.tasks.v4.task_contracts import ActionSequencingTasks
from conversation_core.shared.models.answer_profile import ActionSequence
from conversation_core.shared.models.v2.conversation_entry import ConversationEntryMetadata
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)


class CommonIRTask(CommonIRTasks):
    @shared_task(
        bind=True,
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name=Constants.REQUEST_EVALUATOR)
    def request_evaluator_task(
            self,
            user_input: str,
            conversation_history: Optional[List[str]],
            conversation_action_type: ConversationActionType,
            answer_solution_profile: str,
            pipeline_name: str,
            subscribed_skills: Optional[List[str]],
            auth_token: str,
            meta_data: Optional[dict] = None,
    ):
        pass


class ActionSequenceTaskV4(ActionSequencingTasks):
    @shared_task(
        bind=True,
        autoretry_for=(Exception,),
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name=Constants.START_ACTION_SEQUENCE
    )
    def start_next_action_task(self,
                               user_id: str,
                               user_input: str,
                               answer_solution_profile: str,
                               additional_user_inputs: Optional[dict],
                               overrides: Optional[dict],
                               filters: Optional[dict],
                               conversation_id: str,
                               auth_token: str,
                               task_action_sequence: ActionSequence,
                               action_sequences: List[ActionSequence],
                               user_session: Optional[dict] = None,
                               meta_data: Optional[dict] = None,
                               conversation_entry_metadata: Optional[dict] = None,
                               subscribed_skills: Optional[List[AalpSkill]] = None,
                               route_to_green: bool = False):
        logger.info("Task Queued!")


class IntentResolverCachedTask(IntentResolverTasks):
    @shared_task(
        bind=True,
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name=Constants.EVALUATE_CACHED_INTENT
    )
    def evaluate_intent_task(self,
                             is_new_conversation: bool,
                             user_id: str,
                             user_input: str,
                             answer_solution_profile: str,
                             jurisdictions_override: Optional[List[str]],
                             content_types_override: Optional[List[str]],
                             conversation_id: str,
                             conversation_entry_id: str,
                             conversation_action_type: str,
                             auth_token: str,
                             user_session: dict = None,
                             meta_data: Optional[dict] = None):
        pass


class CachedConversationTask(ConversationTasks):
    @shared_task(bind=True,
                 retry_backoff=True,
                 retry_kwargs={"max_retries": 1},
                 name=Constants.START_CACHED_CONVERSATION)
    def start_conversation_task(self,
                                is_new_conversation: bool,
                                user_id: str,
                                user_input: str,
                                answer_solution_profile: str,
                                jurisdictions_override: Optional[List[str]],
                                content_types_override: Optional[List[str]],
                                conversation_id: str,
                                conversation_entry_id: str,
                                conversation_action_type: ConversationActionType,
                                auth_token: str,
                                user_session: dict = None,
                                meta_data: Optional[dict] = None):
        pass


class IntentResolverTask(IntentResolverTasks):
    @shared_task(
        bind=True,
        autoretry_for=(Exception,),
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name=Constants.EVALUATE_INTENT,
    )
    def evaluate_intent_task(self,
                             is_new_conversation: bool,
                             user_id: str,
                             user_input: str,
                             answer_solution_profile: str,
                             jurisdictions_override: Optional[List[str]],
                             content_types_override: Optional[List[str]],
                             conversation_id: str,
                             conversation_entry_id: str,
                             conversation_action_type: ConversationActionType,
                             auth_token: str,
                             user_session: dict = None,
                             meta_data: Optional[dict] = None):
        logger.info("Task Queued!")


class ConversationTaskV2(ConversationTasks):
    @shared_task(
        bind=True,
        autoretry_for=(Exception,),
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name=Constants.START_CONVERSATION_V2
    )
    def start_conversation_task(self,
                                is_new_conversation: bool,
                                user_id: str,
                                user_input: str,
                                answer_solution_profile: str,
                                jurisdictions_override: Optional[List[str]],
                                content_types_override: Optional[List[str]],
                                conversation_id: str,
                                conversation_entry_id: str,
                                conversation_action_type: ConversationActionType,
                                auth_token: str,
                                user_session: dict = None,
                                meta_data: Optional[dict] = None):
        logger.info("Task Queued!")


class CobaltConversationTask(CobaltConversation):
    @shared_task(
        bind=True,
        autoretry_for=(Exception,),
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name="default:start_conversation",
    )
    def start_conversation_task(
            self,
            is_new_conversation: bool,
            user_id: str,
            user_input: str,
            answer_solution_profile: str,
            jurisdictions_override: Optional[List[str]],
            content_types_override: Optional[List[str]],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            cobalt_session: dict = None,
    ):
        logger.info("Task Queued!")

    @shared_task(
        bind=True,
        autoretry_for=(Exception,),
        retry_backoff=True,
        retry_kwargs={"max_retries": 1},
        name="summarize",
    )
    def start_summarize_task(
            self,
            user_id: str,
            legacy_id: str,
            headnote_id: str,
            answer_solution_profile: str,
            citing_case_ids: List[str],
            conversation_id: str,
            conversation_entry_id: str,
            conversation_action_type: ConversationActionType,
            auth_token: str,
            cobalt_session: dict = None,
    ):
        logger.info("Task Queued!")


def send_conversation_task_v2(
        is_new_conversation: bool,
        user_id: str,
        user_input: str,
        answer_solution_profile: str,
        jurisdictions_override: Optional[List[str]],
        content_types_override: Optional[List[str]],
        conversation_id: str,
        conversation_entry_id: str,
        conversation_action_type: ConversationActionType,
        auth_token: str,
        meta_data: dict,
        user_session: dict = None,
        route_to_green: Optional[bool] = False,
        is_cached_conversation: Optional[bool] = False):
    task_id = conversation_entry_id
    task_impl = conversation_cached_task if is_cached_conversation else conversation_task_v2
    task = task_impl.start_conversation_task.apply_async(
        args=[
            is_new_conversation,
            user_id,
            user_input,
            answer_solution_profile,
            jurisdictions_override,
            content_types_override,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            user_session,
            meta_data,
        ],
        kwargs={},
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "conversation_id": conversation_id,
                 "green": route_to_green,
                 "user_classification": meta_data.get("user_classification", "unknown")},
    )
    logger.info("end create_conversation_entry_v2 with task_id=" + task.task_id)


def evaluate_intent(is_new_conversation: bool,
                    user_id: str,
                    user_input: str,
                    answer_solution_profile: str,
                    jurisdictions_override: Optional[List[str]],
                    content_types_override: Optional[List[str]],
                    conversation_id: str,
                    conversation_entry_id: str,
                    conversation_action_type: ConversationActionType,
                    auth_token: str,
                    meta_data: dict,
                    user_session: dict = None,
                    route_to_green: Optional[bool] = False,
                    is_cached_intent: Optional[bool] = False):
    task_id = f"{conversation_entry_id}-i"
    task_impl = intent_resolver_cached_task if is_cached_intent else intent_resolver_task
    task = task_impl.evaluate_intent_task.apply_async(
        args=[
            is_new_conversation,
            user_id,
            user_input,
            answer_solution_profile,
            jurisdictions_override,
            content_types_override,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            user_session,
            meta_data,
        ],
        kwargs={},
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "conversation_id": conversation_id,
                 "green": route_to_green,
                 "user_classification": meta_data.get("user_classification", "unknown")},
    )
    logger.info("end evaluate_intent_entry with task_id=" + task.task_id)


conversation_task = CobaltConversationTask()
conversation_cached_task = CachedConversationTask()
action_sequence_task_v4 = ActionSequenceTaskV4()
conversation_task_v2 = ConversationTaskV2()
intent_resolver_task = IntentResolverTask()
intent_resolver_cached_task = IntentResolverCachedTask()


def send_summarize_task(
        user_id: str,
        legacy_id: str,
        headnote_id: str,
        citing_case_ids: List[str],
        answer_solution_profile: str,
        conversation_id: str,
        conversation_entry_id: str,
        conversation_action_type: ConversationActionType,
        auth_token: str,
        cobalt_session: dict = None,
        route_to_green: Optional[bool] = False,
        user_classification: Optional[str] = "unknown",
):
    task = conversation_task.start_summarize_task.apply_async(
        args=[
            user_id,
            legacy_id,
            headnote_id,
            answer_solution_profile,
            citing_case_ids,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            cobalt_session,
        ],
        kwargs={},
        task_id=conversation_entry_id,
        headers={"time_sent": time.time(), "conversation_id": conversation_id,
                 "green": route_to_green, "user_classification": user_classification},
    )
    logger.info("end create_conversation_entry with task_id=" + task.task_id)


def send_conversation_task(
        is_new_conversation: bool,
        user_id: str,
        user_input: str,
        answer_solution_profile: str,
        jurisdictions_override: Optional[List[str]],
        content_types_override: Optional[List[str]],
        conversation_id: str,
        conversation_entry_id: str,
        conversation_action_type: ConversationActionType,
        auth_token: str,
        cobalt_session: dict = None,
        route_to_green: Optional[bool] = False,
        version: Optional[str] = "v1",
        user_classification: Optional[str] = "unknown",
):
    task_id = conversation_entry_id
    task = conversation_task.start_conversation_task.apply_async(
        args=[
            is_new_conversation,
            user_id,
            user_input,
            answer_solution_profile,
            jurisdictions_override,
            content_types_override,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            cobalt_session,
        ],
        kwargs={},
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "conversation_id": conversation_id,
                 "green": route_to_green,
                 "user_classification": user_classification},
    )
    logger.info("end create_conversation_entry with task_id=" + task.task_id)


def send_action_sequence_task(
        task_id: str,
        user_id: str,
        user_input: str,
        answer_solution_profile: str,
        additional_user_inputs: Optional[dict],
        overrides: Optional[dict],
        filters: Optional[dict],
        conversation_id: str,
        auth_token: str,
        meta_data: Optional[dict],
        conversation_entry_metadata: Optional[ConversationEntryMetadata],
        subscribed_skills: Optional[List[AalpSkill]],
        task_action_sequence: ActionSequence,
        action_sequences: List[ActionSequence],
        user_session: dict = None,
        route_to_green: Optional[bool] = False) -> str:
    task_impl = action_sequence_task_v4
    task = task_impl.start_next_action_task.apply_async(
        args=[],
        kwargs={"user_id": user_id,
                "user_input": user_input,
                "additional_user_inputs": additional_user_inputs,
                "answer_solution_profile": answer_solution_profile,
                "overrides": overrides,
                "filters": filters,
                "conversation_id": conversation_id,
                "auth_token": auth_token,
                "task_action_sequence": task_action_sequence,
                "action_sequences": action_sequences,
                "user_session": user_session,
                "meta_data": meta_data,
                "conversation_entry_metadata": conversation_entry_metadata,
                "subscribed_skills": subscribed_skills,
                "route_to_green": route_to_green
                },
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "conversation_id": conversation_id,
                 "green": route_to_green,
                 "user_classification": meta_data.get("user_classification", "unknown")},
    )
    logger.info("end start_next_action_task with task_id=" + task.task_id)

    return task_id
