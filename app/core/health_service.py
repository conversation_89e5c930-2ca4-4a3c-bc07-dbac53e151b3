import gc
import os
import platform
import threading
import time

import psutil
from app.config.settings import Settings
from config import settings
from datadog_utils import metrics_client
from filelock import FileLock
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)
settings: Settings = settings.get_settings()


class HealthService:
    def __init__(self):
        if platform.system() == "Windows":
            if settings.TEMP_FOLDER:
                temp_folder = "/temp"
            else:
                temp_folder = settings.TEMP_FOLDER
        else:
            if "temp" in settings.TEMP_FOLDER:
                temp_folder = "/tmp"
            else:
                temp_folder = settings.TEMP_FOLDER

        logger.info(f"Starting the health service. Using {temp_folder}")
        if not os.path.isdir(temp_folder):
            os.makedirs(temp_folder, mode=0o777, exist_ok=False)
        self.pod_name = os.getenv("HOSTNAME", "unknown")
        self.lock_file = f"{temp_folder}/bad_state.lock"
        self.bad_state_file = f"{temp_folder}/bad_state.txt"
        logger.info(f"Health service has been initialized. Thread id: {threading.get_ident()}")

    def should_terminate(self) -> bool:
        if not os.path.isfile(self.bad_state_file):
            return False

        now = time.time()
        try:
            with open(self.bad_state_file, "r") as bad_file:
                bad_state_time = float(bad_file.read())
        except Exception as ex:
            logger.warn(ex)
            bad_state_time = time.time()

        total_elapsed = now - bad_state_time
        total_time_in_current_state = round(total_elapsed)
        if bad_state_time > 0.0:
            logger.warn(
                f"{self.pod_name} has been in a bad state for {total_time_in_current_state} seconds.  Thread id: {threading.get_ident()}"
            )
            metrics_client.send_metric(
                "pod.badStateDurationSeconds",
                total_time_in_current_state,
                [f"pod_name:{self.pod_name}", f"thread_id:{threading.get_ident()}"],
            )
        if total_time_in_current_state > 600.0:
            return True
        else:
            return False

    async def check_memory(self) -> bool:
        try:
            # percent_used = memory_usage[2]
            # used_memory = memory_usage[3]
            if os.path.isfile("/sys/fs/cgroup/memory/memory.limit_in_bytes"):
                with open("/sys/fs/cgroup/memory/memory.limit_in_bytes") as f:
                    total_physical_memory = int(f.read())
            else:
                total_physical_memory = psutil.virtual_memory().total

            pids = {}
            total_process_memory = 0

            for proc in psutil.process_iter():
                if "python" in proc.name().lower() or "gunicorn" in proc.name().lower():
                    p = proc.pid.__str__()
                    pid_total = round(psutil.Process(proc.pid).memory_info().rss / 1024**2, 2)
                    pids[p] = pid_total
                    total_process_memory = total_process_memory + pid_total

                    metrics_client.send_metric(
                        "workers.processMemory", pid_total, [f"processId:{p}", "pod_name:{" "self.pod_name}"]
                    )

            metrics_client.send_metric(
                "workers.totalPhysicalMemory",
                round(total_physical_memory / 1000000000, 2),
                [f"pod_name:{self.pod_name}"],
            )
            metrics_client.send_metric(
                "workers.totalProcessMemory", round(total_process_memory, 2), [f"pod_name:{self.pod_name}"]
            )

            percent_used = round(((total_process_memory / 1000) / (total_physical_memory / 1000000000) * 100), 2)
            metrics_client.send_metric("workers.percentMemoryUsed", percent_used, [f"pod_name:{self.pod_name}"])

            if percent_used >= 75.0:
                if not os.path.isfile(self.bad_state_file):
                    lock = FileLock(self.lock_file, timeout=1)
                    with lock:
                        with open(self.bad_state_file, "w") as f:
                            f.write(str(time.time()))

                total_cleaned = gc.collect()
                logger.warn(
                    f"Total memory used exceeds 75%. Total objects cleaned during gc: {total_cleaned}. Thread id: {threading.get_ident()}"
                )
                return False
            else:
                if os.path.isfile(self.bad_state_file):
                    logger.info(f"Pod has recovered and now is back in service. Thread id: {threading.get_ident()}")
                    lock = FileLock(self.lock_file, timeout=1)
                    with lock:
                        os.remove(self.bad_state_file)
                return True
        except Exception as e:
            logger.warn(e)
            if not os.path.isfile(self.bad_state_file):
                lock = FileLock(self.lock_file, timeout=1)
                with lock:
                    with open(self.bad_state_file, "w") as f:
                        f.write(str(time.time()))
            return False
