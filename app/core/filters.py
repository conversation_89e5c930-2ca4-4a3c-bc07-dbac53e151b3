import logging


class EndpointFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        if record.levelname != logging.DEBUG:
            return record.getMessage().find("/health") == -1


class DatadogStartupFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        if record.getMessage().find("Failed to start collector MemoryCollector") == 0:
            record.levelname = "WARN"

        return True
