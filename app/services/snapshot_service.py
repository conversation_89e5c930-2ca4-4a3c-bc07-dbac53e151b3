import uuid
from typing import Annotated, Optional, List
from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException, ConversationDB as ConversationDBV2, \
    SnapshotDB
from conversation_core.shared.models.v2.conversation import ConversationSnapshot, ConversationSnapshotResponse, Conversation as ConversationV2
from conversation_core.shared.models.v3.conversation import ConversationV3, conversation_v2_to_v3, ActionSequence, ActionSequenceTask
from conversation_core.shared.utils.trace import add_to_current_span, add_trace_context_to_current_span
from conversation_core.shared.utils.version_info import get_non_rag_version_info
from fastapi import Depends, Request
from raslogger import LoggingFactory
from starlette import status
from fastapi import HTTPException
from celery.result import AsyncResult

from app.api.auth import gcs_auth
from app.api.models.conversations.user_session_model import UserSession
from app.config import settings
from app.config.settings import Settings
from app.utils.span_utils import set_span_http_code, raise_http_error
from app.services.chat_profile_service import answer_profile_service
from app.services.conversation_creation_service import get_action_sequences, get_latest_action_sequence_task
from conversation_core.shared.enums import ConversationActionType

logger = LoggingFactory.get_logger(__name__)
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
snapshot_db= SnapshotDB(settings.SNAPSHOT_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]


def log_version_info(user_id: str):
    version_info = get_non_rag_version_info()
    add_to_current_span("version_info.app_version", version_info.get("app_version"))
    add_to_current_span("version_info.python_version", version_info.get("python_version"))
    add_to_current_span("user_id", user_id)

def append_disclaimer(conversation_entry,
                      profile_name: str):
    if profile_name is not None:
        if conversation_entry.result is None:
            return

        answer_profile = answer_profile_service.get_profile(name=profile_name)
        include_disclaimer = getattr(answer_profile, Constants.LLM_AI_INCLUDE_DISCLAIMER_FLAG, True)
        if not include_disclaimer:
            return

        disclaimer_constant = getattr(answer_profile, Constants.LLM_DISCLAIMER_CONSTANT, "LLM_AI_DEFAULT_DISCLAIMER")
        disclaimer_override_text = getattr(answer_profile, Constants.LLM_DISCLAIMER_OVERRIDE_TEXT_KEY, None)
        system_output_key = getattr(answer_profile, Constants.CONV_SYSTEM_OUTPUT_KEY, "response")
        disclaimer_use_html = getattr(answer_profile, Constants.LLM_DISCLAIMER_USE_HTML, False)

        logger.info(f"Include disclaimer: {include_disclaimer} for profile: {profile_name} :: "
                    f" System output key: {system_output_key} :: disclaimer_constant: {disclaimer_constant}")

        system_output = conversation_entry.result.system_output
        disclaimer_text = disclaimer_override_text if disclaimer_override_text is not None else getattr(Constants,
                                                                                                        disclaimer_constant)

        if system_output_key in system_output:
            if disclaimer_text not in system_output[system_output_key]:
                if disclaimer_use_html:
                    system_output[system_output_key] += "<p>" + disclaimer_text.replace("\n", "<br/>") + "</p>"
                else:
                    system_output[system_output_key] += disclaimer_text
        else:
            system_output['disclaimer'] = disclaimer_text
    else:
        logger.info("Profile name is None. Skipping appending disclaimer.")


def create_conversation_snapshot(
    request: Request,
    user_id: str,
    conversation_id: str,
    auth_token: str = Depends(gcs_auth.authorize_with_rights)
):
    log_version_info(user_id)
    user_session = UserSession(request)
    add_trace_context_to_current_span(conversation_id, "", user_session.get_session_info())

    logger.info(f"Fetching Conversation History for conversation_id: {conversation_id}")
    conversation_data: dict = dynamo_db_v2.get_conversation_snapshot_data(
        user_id=user_id,
        conversation_id=conversation_id
    )
    if conversation_data is None:
        raise_http_error(code=status.HTTP_404_NOT_FOUND, detail=f"No Conversation details found for conversation_id: {conversation_id}")

    snapshot_id = str(uuid.uuid4())

    logger.info(f"Creating Snapshot for conversation_id: {conversation_id} and user_id: {user_id}")
    try:
        snapshot_db.write_conversation_snapshot(snapshot_id=snapshot_id,
                                                user_id=user_id,
                                                conversation_id=conversation_id,
                                                conversation_entries=conversation_data.get(Constants.CONVERSATION_ENTRY_IDS),
                                                ttl=conversation_data.get(Constants.CONV_TTL))
    except:
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create Snapshot for conversation_id: {conversation_id} and user_id: {user_id}")

    logger.info(f"Created Snapshot for conversation_id: {conversation_id} and user_id: {user_id}")
    set_span_http_code(status.HTTP_200_OK)
    return ConversationSnapshot(user_id=user_id, snapshot_id=snapshot_id, conversation_id=conversation_id)


def get_conversation_snapshot(snapshot_id: str):
    try:
        from_v3_endpoint = False

        logger.info(f"Fetching Conversation Snapshot for snapshot_id: {snapshot_id}")
        snapshot: ConversationSnapshotResponse = snapshot_db.get_conversation_snapshot(
            snapshot_id=snapshot_id,
            consistent_read=True
        )
        if snapshot is None:
            raise_http_error(code=status.HTTP_404_NOT_FOUND, detail=f"No Snapshot details found for SnapshotID: {snapshot_id}")
        logger.info(f"Returning Conversation Snapshot for snapshot_id: {snapshot_id}")
        conversation: ConversationV2 = dynamo_db_v2.get_conversation(
            user_id=snapshot.user_id,
            conversation_id=snapshot.conversation_id,
            conversation_entry_ids=snapshot.conversation_entries,
            consistent_read=True,
            include_rag_pipeline_output=False,
        )

        if conversation is None:
            raise_http_error(code=status.HTTP_404_NOT_FOUND, detail=f"No Conversation details found for SnapshotID: {snapshot_id} and ConversationID: {snapshot.conversation_id}")

        conversation.conversation_entries.sort(key=lambda x: x.timestamp, reverse=True)
        conversation_v3: ConversationV3 = conversation_v2_to_v3(conversation,
                                                                Constants.QUERY_ANNOTATION_TYPES,
                                                                Constants.INTENT_CATEGORY_ANNOTATION_TYPES,
                                                                Constants.OUT_OF_SCOPE_INTENT_CATEGORY_ANNOTATION_TYPES,
                                                                Constants.JURISDICTION_ANNOTATION_TYPES)

        profile_name = None
        if conversation.conversation_metadata and conversation.conversation_metadata.profile:
            profile_name = conversation.conversation_metadata.profile

        if conversation_v3.conversation_metadata and profile_name:
            action_sequences: Optional[List[ActionSequence]] = get_action_sequences(profile=profile_name)
            from_v3_endpoint = True if action_sequences else False
            latest_action_sequence_task: Optional[ActionSequenceTask] = None
            if from_v3_endpoint and action_sequences:
                conversation_v3.conversation_metadata.conversation_action_sequence = action_sequences
                latest_action_sequence_task = get_latest_action_sequence_task(conversation=conversation_v3,
                                                                              action_sequences=action_sequences)
            if from_v3_endpoint and latest_action_sequence_task:
                conversation_v3.conversation_metadata.current_action_sequence_task = latest_action_sequence_task

        conversation = conversation_v3 if from_v3_endpoint else conversation

        for conversation_entry in conversation.conversation_entries:

            #Since it's v2 response we don't need these fields.
            if not from_v3_endpoint:
                conversation_entry.user_interaction_required = None
                conversation_entry.additional_user_inputs_store_type = None

            if conversation_entry.conversation_action_type == ConversationActionType.RAG:
                append_disclaimer(conversation_entry=conversation_entry, profile_name=profile_name)

        set_span_http_code(status.HTTP_200_OK)
        return conversation
    except HTTPException as HE:
        raise HE
    except Exception as e:
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Something is wrong in fetching details for SnapshotID: {snapshot_id}, Error Details: {str(e)}")
