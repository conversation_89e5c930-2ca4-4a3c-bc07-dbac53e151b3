import time
import uuid
import json
from sys import getsizeof
from http.client import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import List, Optional, <PERSON>ple

import common_rest_utils.constants
from celery.result import AsyncResult
from conversation_core.shared.worker.worker_task import WorkerTask, DEBUG_GROUP_KEY
from pydantic.fields import FieldInfo
from common_utils.json_utils import JsonUtils
from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper import ConversationDB
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2, DynamoDBNotFoundException
from conversation_core.shared.enums import ConversationActionType, DataRetention, AalpSkill
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.models.v2.conversation import ConversationMetadata as ConversationMetadataV2, \
    StartConversationResponse, StartConversationRequest, Conversation as ConversationV2, CustomMetadata
from conversation_core.shared.models.v2.conversation import CustomMetadata as ConversationCustomMetadata
from conversation_core.shared.models.v3.conversation import ConversationV3, ActionSequenceTask
from conversation_core.shared.models.v3.conversation_entry import ConversationEntryV3
from conversation_core.shared.models.v2.conversation_entry import \
    ConversationEntryMetadata as ConversationEntryMetadataV2, ConversationEntry, ConversationEntryUserInputStoreType
from conversation_core.shared.models.v2.conversation_entry import CustomMetadata as ConversationEntryCustomMetadata, \
    EmailNotification
from conversation_core.shared.models.v3.conversation import StartConversationRequestV3
from conversation_core.shared.profile_mapping_util import get_remapped_profile_name
from conversation_core.shared.services.conversation_service_base import RetrieveConversationEntryStatuses
from conversation_core.shared.utils.trace import add_to_current_span, add_trace_context_to_current_span
from conversation_core.shared.utils.action_sequence_util import ActionSequenceUtil
from ddtrace import tracer
from fastapi import Depends, Request, HTTPException
from ras_events_client.event_service import send_event
from ras_events_client.models.event_models import UserRequestHeaderProperties, GenericEventV2, \
    ConversationEventV2, ConversationReceivedProperties, ConversationType
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from api.models.conversations.submit_answer_models import StartAnswerGenerationRequest
from api.models.conversations.user_session_model import UserSession
from app.api.models.conversations.submit_answer_models import (
    StartAnswerGenerationResponse,
    SummarizeAnswerGenerationRequest,
)
from app.config.settings import Settings
from app.tasks.shared.conversation_tasks import evaluate_intent, send_summarize_task, send_action_sequence_task
from config import settings
from services.chat_profile_service import answer_profile_service
from tasks.shared.conversation_tasks import send_conversation_task_v2, send_conversation_task
from utils.span_utils import raise_http_error, add_user_classification_to_span, set_span_http_code

logger = LoggingFactory.get_logger(__name__)
settings: Settings = settings.get_settings()
dynamo_db = ConversationDB(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
action_sequence_util = ActionSequenceUtil(settings, dynamo_db_v2, answer_profile_service)


def check_forbidden_attrs(forbidden_attrs: set[str], updated_attributes: dict[str, ...]):
    found_forbidden_updates = set(updated_attributes).intersection(forbidden_attrs)
    if found_forbidden_updates:
        raise_http_error(
            status.HTTP_400_BAD_REQUEST,
            f"Attribute {', '.join(found_forbidden_updates)} is forbidden to update.",
        )


def prevent_metadata_update(
        conversation_metadata: ConversationMetadataV2 = None,
        conversation_entry_metadata: ConversationEntryMetadataV2 = None
):
    if conversation_metadata:
        forbidden_attrs = {
            "profile",
            "created_timestamp",
            "data_retention",
            "latest_entry_date",
            "latest_entry_status",
            "max_followup_date",
        }
        check_forbidden_attrs(forbidden_attrs, conversation_metadata.model_dump(exclude_defaults=True))
    if conversation_entry_metadata and conversation_entry_metadata.email_notification:
        check_forbidden_attrs(
            {"email_sent"}, conversation_entry_metadata.email_notification.model_dump(exclude_defaults=True)
        )

def process_conversation_metadata(
        conversation_metadata: ConversationMetadataV2
) -> (dict, ConversationCustomMetadata):
    """
    Convert ConversationMetadataV2 to a dictionary and ConversationCustomMetadata.
    Remove custom and email_notification attributes from conversation_metadata
    """
    if conversation_metadata is None:
        return {}, None

    custom_conversation_metadata: Optional[CustomMetadata] = getattr(conversation_metadata, "custom", None)
    if hasattr(conversation_metadata, "custom"):
        del conversation_metadata.custom

    email_notification: Optional[EmailNotification] = getattr(conversation_metadata, "email_notification", None)
    if hasattr(conversation_metadata, "email_notification"):
        del conversation_metadata.email_notification

    attribute_updates = conversation_metadata.model_dump(exclude_none=True, by_alias=True) if conversation_metadata else {}
    if email_notification:
        attribute_updates.update(email_notification.model_dump(exclude_none=True, by_alias=True))

    return attribute_updates, custom_conversation_metadata


def start_or_continue_conversation_v3(
        request: Request,
        user_id: str,
        is_new_conversation: bool,
        conversation_id: str,
        start_conversation_request: StartConversationRequestV3,
        conversation_type: str,
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        from_v2_endpoint: Optional[bool] = False,
        static_entry_ids: bool = False
) -> StartConversationResponse:
    if not is_new_conversation:
        prevent_metadata_update(
            start_conversation_request.conversation_metadata, start_conversation_request.conversation_entry_metadata
        )

    user_session = UserSession(request)
    user_classification = add_user_classification_to_span(user_session.get_session_info())

    answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=start_conversation_request.answer_solution_profile.lower())

    conversation_metadata = (ConversationMetadataV2()
                             if start_conversation_request.conversation_metadata is None
                             else start_conversation_request.conversation_metadata)

    if is_new_conversation:
        title: str = (conversation_metadata.title if conversation_metadata.title is not None else
                      start_conversation_request.user_input)
    else:
        title: str = (conversation_metadata.title if conversation_metadata.title is not None else None)

    max_title_length: int = 197  # TODO: Move to constant

    if title is not None and len(title) >  max_title_length:
        title = title[:max_title_length] + "..."

    max_followup_date = None
    max_hours: int = getattr(answer_profile, Constants.PROFILE_MAX_CONVERSATION_TIME_HOURS, -1)
    if max_hours > -1:
        max_followup_date = int(time.time()) + (max_hours * 60 * 60)  # hours to seconds

    start_conversation_request.overrides = start_conversation_request.overrides or {}
    start_conversation_request.filters = start_conversation_request.filters or {}

    # Use override if present, else use profile setting
    run_intent_resolver_override = start_conversation_request.overrides.get(
        Constants.RUN_INTENT_RESOLVER_OVERRIDE, None)
    run_intent_resolver: bool = run_intent_resolver_override \
        if run_intent_resolver_override is not None else getattr(answer_profile,
                                                                 Constants.PROFILE_INTENT_TASK_ENABLED, False)

    conversation_entry_metadata = (ConversationEntryMetadataV2()
                                   if start_conversation_request.conversation_entry_metadata is None
                                   else start_conversation_request.conversation_entry_metadata)
    # Setting up Email notification properties such as sent_email and result_viewed to their default values (False)
    if conversation_entry_metadata.email_notification:
        conversation_entry_metadata.email_notification.email_sent = False
    if conversation_entry_metadata.custom and conversation_entry_metadata.custom.result_viewed:
        conversation_entry_metadata.custom.result_viewed.is_result_viewed = False

    # V2 routed task
    if from_v2_endpoint:
        run_intent_resolver_override = start_conversation_request.overrides.get(Constants.RUN_INTENT_RESOLVER_OVERRIDE,
                                                                                None)
        run_intent_resolver: bool = run_intent_resolver_override \
            if run_intent_resolver_override is not None else getattr(answer_profile,
                                                                     Constants.PROFILE_INTENT_TASK_ENABLED, False)

        start_conversation_response: StartConversationResponse = create_conversation_entry_v2(
            is_new_conversation=is_new_conversation,
            auth_token=auth_token,
            user_session=user_session.get_session_info(),
            request=request,
            user_id=user_id,
            conversation_id=conversation_id,
            user_input=start_conversation_request.user_input,
            profile=start_conversation_request.answer_solution_profile,
            jurisdictions_override=start_conversation_request.overrides.get(Constants.JURISDICTIONS_OVERRIDE, []),
            content_types_override=start_conversation_request.overrides.get(Constants.CONTENT_TYPES_OVERRIDE, []),
            content_types_exclude=start_conversation_request.filters.get(Constants.CONTENT_TYPES_OVERRIDE, []),
            product=conversation_metadata.product,
            title=title,
            custom_conversation_metadata=conversation_metadata.custom,
            email_notification=conversation_entry_metadata.email_notification,
            custom_conversation_entry_metadata=conversation_entry_metadata.custom,
            run_intent_resolver=run_intent_resolver,
            auto_submit_task=True,
            user_classification=user_classification,
            conversation_type=conversation_type,
            data_retention=(DataRetention(conversation_metadata.data_retention)
                            if conversation_metadata.data_retention is not None
                            else Constants.CONV_DATA_RETENTION_DEFAULT),
            is_favorite=conversation_metadata.is_favorite,
            max_followup_date=max_followup_date,
            static_entry_ids=static_entry_ids,
        )
    # V3 routed conversation
    else:
        user_session_dict = user_session.get_session_info()
        if not user_session_dict.get(common_rest_utils.constants.Constants.ORIGINATING_SERVICE_NAME):
            user_session_dict[
                common_rest_utils.constants.Constants.ORIGINATING_SERVICE_NAME] = start_conversation_request.answer_solution_profile
            
        start_conversation_response: StartConversationResponse = create_conversation_task_v3(
            is_new_conversation=is_new_conversation,
            auth_token=auth_token,
            user_session=user_session_dict,
            request=request,
            user_id=user_id,
            conversation_id=conversation_id,
            user_input=start_conversation_request.user_input,
            additional_user_inputs=getattr(start_conversation_request, Constants.ADDITIONAL_USER_INPUTS, {}),
            profile=start_conversation_request.answer_solution_profile,
            overrides=getattr(start_conversation_request, Constants.OVERRIDES, {}),
            filters=getattr(start_conversation_request, Constants.FILTERS, {}),
            product=conversation_metadata.product,
            title=title,
            conversation_metadata=start_conversation_request.conversation_metadata,
            conversation_entry_metadata=conversation_entry_metadata,
            subscribed_skills=start_conversation_request.subscribed_skills,
            conversation_type=conversation_type,
            data_retention=(DataRetention(conversation_metadata.data_retention)
                            if conversation_metadata.data_retention is not None
                            else Constants.CONV_DATA_RETENTION_DEFAULT),
            is_favorite=conversation_metadata.is_favorite,
            max_followup_date=max_followup_date,
            user_classification=user_classification,
            static_entry_ids=static_entry_ids
        )
    add_to_current_span(Constants.PROFILE, start_conversation_request.answer_solution_profile.lower())
    add_trace_context_to_current_span(start_conversation_response.conversation_id,
                                      start_conversation_response.conversation_entry_id,
                                      user_session.get_session_info())

    set_span_http_code(status.HTTP_200_OK)
    return start_conversation_response


def start_or_continue_conversation_v2(
        request: Request,
        user_id: str,
        is_new_conversation: bool,
        conversation_id: str,
        start_conversation_request: StartConversationRequest,
        conversation_type: str,
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        static_entry_ids: bool = False
) -> StartConversationResponse:
    add_to_current_span("bluegreen_route", request.headers.get("x-bluegreen-routing", default="blue").lower())

    start_conversation_request_v3 = StartConversationRequestV3(
        user_input=start_conversation_request.user_input,
        answer_solution_profile=start_conversation_request.answer_solution_profile,
        overrides={Constants.JURISDICTIONS_OVERRIDE: start_conversation_request.jurisdictions_override,
                   Constants.CONTENT_TYPES_OVERRIDE: start_conversation_request.content_types_override},
        filters={Constants.CONTENT_TYPES_OVERRIDE: start_conversation_request.content_types_exclude},
        conversation_metadata=start_conversation_request.conversation_metadata,
        conversation_entry_metadata=start_conversation_request.conversation_entry_metadata,
    )

    if start_conversation_request.intent_resolver_override:
        start_conversation_request_v3.overrides[Constants.RUN_INTENT_RESOLVER_OVERRIDE] = (
            not start_conversation_request.intent_resolver_override
            # New logic is backwards of how it was before, so we need to negate it
        )

    return start_or_continue_conversation_v3(
        request=request,
        user_id=user_id,
        is_new_conversation=is_new_conversation,
        conversation_id=conversation_id,
        start_conversation_request=start_conversation_request_v3,
        conversation_type=conversation_type,
        auth_token=auth_token,
        static_entry_ids=static_entry_ids,
        from_v2_endpoint=True
    )


def create_summarize_conversation_entry_v2(
        self,
        request: Request,
        auth_token: str,
        cobalt_session: dict,
        profile: str,
        user_id: str,
        headnote_id: str,
        legacy_id: str,
        citing_case_ids: List[str],
        product: str,
        user_classification: str,
        data_retention: DataRetention,
        conversation_metadata: ConversationMetadataV2,
        conversation_entry_metadata: ConversationEntryMetadataV2,
) -> StartConversationResponse:
    add_to_current_span("conversation_type", "summarize")

    auth_token = auth_token.replace("Bearer ", "")

    remapped_profile = str(remap_profile(profile).lower())
    add_to_current_span("profile", remapped_profile)

    conversation_id = str(uuid.uuid4())
    conversation_entry_id = str(uuid.uuid4())
    start_conversation_response = StartConversationResponse(user_id=user_id, conversation_id=conversation_id,
                                                            conversation_entry_id=conversation_entry_id)
    add_trace_context_to_current_span(conversation_id, start_conversation_response.conversation_entry_id,
                                      cobalt_session)

    dynamo_db_v2.write_summarize(
        self,
        user_id=user_id,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        product=product,
        profile=remapped_profile,
        legacy_id=legacy_id,
        headnote_id=headnote_id,
        citing_case_ids=citing_case_ids,
        data_retention=data_retention.value,
        user_classification=user_classification,
        custom_conversation_metadata=conversation_metadata.custom,
        custom_conversation_entry_metadata=conversation_entry_metadata.custom,
    )

    generate_conversation_event(
        auth_token=auth_token,
        cobalt_session=cobalt_session,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        request=request,
        profile=remapped_profile,
        action_type=ConversationActionType.KEYCITE,
    )

    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False

    send_summarize_task(
        user_id=user_id,
        legacy_id=legacy_id,
        headnote_id=headnote_id,
        citing_case_ids=citing_case_ids,
        answer_solution_profile=remapped_profile,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        conversation_action_type=ConversationActionType.KEYCITE,
        auth_token=auth_token,
        cobalt_session=cobalt_session,
        route_to_green=route_to_green,
        user_classification=user_classification,
    )

    start_conversation_response.conversation_metadata = (
        dynamo_db_v2.get_conversation(user_id=user_id,
                                      conversation_id=start_conversation_response.conversation_id,
                                      include_results=False,
                                      consistent_read=True)
        .conversation_metadata)
    start_conversation_response.conversation_entry_metadata = (
        dynamo_db_v2.get_conversation_entry(user_id=user_id,
                                            conversation_id=start_conversation_response.conversation_id,
                                            conversation_entry_id=start_conversation_response.conversation_entry_id,
                                            consistent_read=True)
        .conversation_entry_metadata)
    return start_conversation_response


def remap_profile(profile: str) -> str:
    # This is a temporary configuration until ras config comes into place. This allows remapping to test other systems
    # without changing Westlaw since the profile is hard coded.
    return get_remapped_profile_name(profile=profile, parameter_store_mapping=settings.DEFAULT_PROFILE_MAPPING_SECRET)


@tracer.wrap()
def create_conversation_entry(
        is_new_conversation: bool,
        user_id: str,
        start_answer_generation_request: StartAnswerGenerationRequest,
        conversation_id: str,
        auth_token: str,
        cobalt_session: dict,
        request: Request,
        email_address: str,
        send_email: bool,
        destination_url: str,
        conversation_type: str,
        run_intent_resolver: bool = False,
        auto_submit_task: bool = True,
        conversation_version: int = 1,
        user_classification: str = "unknown"
):
    auth_token = auth_token.replace("Bearer ", "")
    start_answer_generation_request.answer_solution_profile = remap_profile(
        start_answer_generation_request.answer_solution_profile
    )

    start_answer_generation_response = StartAnswerGenerationResponse(user_id=user_id, conversation_id=conversation_id)
    add_trace_context_to_current_span(conversation_id, start_answer_generation_response.conversation_entry_id,
                                      cobalt_session)
    add_to_current_span("conversation_type", conversation_type)

    answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=start_answer_generation_request.answer_solution_profile.lower())

    create_conversation_received_event(is_new_conversation=is_new_conversation,
                                       user_session=cobalt_session,
                                       conversation_id=conversation_id,
                                       conversation_entry_id=start_answer_generation_response.conversation_entry_id,
                                       profile=start_answer_generation_request.answer_solution_profile.lower(),
                                       auth_token=auth_token,
                                       jurisdictions_override=start_answer_generation_request.jurisdictions_override)

    header_event = GenericEventV2(
        properties=UserRequestHeaderProperties(
            conversationId=conversation_id,
            conversationEntryId=start_answer_generation_response.conversation_entry_id,
            conversationActionType=ConversationActionType.RAG,
            profile=start_answer_generation_request.answer_solution_profile.lower(),
            request_headers=request.headers.items(),
            request_cookies=request.cookies,
        ),
        event_name="RASSearch.AI.UserRequestHeaders",
        correlation_id=start_answer_generation_response.conversation_entry_id,
        user_session=cobalt_session,
    )

    send_event(body=header_event, auth_token=auth_token, wait=True)

    dynamo_db.write_progress(
        user_input=start_answer_generation_request.user_input,
        user_id=start_answer_generation_response.user_id,
        conversation_id=start_answer_generation_response.conversation_id,
        conversation_entry_id=start_answer_generation_response.conversation_entry_id,
        conversation_action_type=ConversationActionType.RAG,
        ttl=settings.DYNAMODB_TTL,
        email_address=email_address,
        send_email=send_email,
        destination_url=destination_url,
        email_sent=False,
    )

    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False

    if run_intent_resolver and answer_profile.intent_enabled():
        evaluate_intent(is_new_conversation=is_new_conversation,
                        user_id=user_id,
                        user_input=start_answer_generation_request.user_input,
                        answer_solution_profile=start_answer_generation_request.answer_solution_profile,
                        jurisdictions_override=start_answer_generation_request.jurisdictions_override,
                        content_types_override=start_answer_generation_request.content_types_override,
                        conversation_id=conversation_id,
                        conversation_entry_id=start_answer_generation_response.conversation_entry_id,
                        conversation_action_type=ConversationActionType.INTENT,
                        auth_token=auth_token,
                        user_session=cobalt_session,
                        route_to_green=route_to_green,
                        meta_data={"auto_submit_task": auto_submit_task,
                                   "route_to_green": route_to_green,
                                   "user_classification": user_classification})
    elif conversation_version == 2:
        send_conversation_task_v2(
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=start_answer_generation_request.user_input,
            answer_solution_profile=start_answer_generation_request.answer_solution_profile,
            jurisdictions_override=start_answer_generation_request.jurisdictions_override,
            content_types_override=start_answer_generation_request.content_types_override,
            conversation_id=conversation_id,
            conversation_entry_id=start_answer_generation_response.conversation_entry_id,
            conversation_action_type=ConversationActionType.RAG,
            auth_token=auth_token,
            user_session=cobalt_session,
            route_to_green=route_to_green,
            meta_data={"auto_submit_task": auto_submit_task,
                       "route_to_green": route_to_green,
                       "user_classification": user_classification}
        )
    else:
        send_conversation_task(
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=start_answer_generation_request.user_input,
            answer_solution_profile=start_answer_generation_request.answer_solution_profile,
            jurisdictions_override=start_answer_generation_request.jurisdictions_override,
            content_types_override=start_answer_generation_request.content_types_override,
            conversation_id=conversation_id,
            conversation_entry_id=start_answer_generation_response.conversation_entry_id,
            conversation_action_type=ConversationActionType.RAG,
            auth_token=auth_token,
            cobalt_session=cobalt_session,
            route_to_green=route_to_green,
            user_classification=user_classification,
        )

    return start_answer_generation_response


@tracer.wrap()
def create_summarize_conversation_entry(
        user_id: str,
        summarize_answer_generation_request: SummarizeAnswerGenerationRequest,
        conversation_id: str,
        auth_token: str,
        cobalt_session: dict,
        request: Request,
        user_classification: Optional[str] = None
):
    auth_token = auth_token.replace("Bearer ", "")
    summarize_answer_generation_request.answer_solution_profile = remap_profile(
        summarize_answer_generation_request.answer_solution_profile
    )
    start_answer_generation_response = StartAnswerGenerationResponse(user_id=user_id, conversation_id=conversation_id)
    add_trace_context_to_current_span(conversation_id, start_answer_generation_response.conversation_entry_id,
                                      cobalt_session)

    header_event = GenericEventV2(
        properties=UserRequestHeaderProperties(
            conversationId=conversation_id,
            conversationEntryId=start_answer_generation_response.conversation_entry_id,
            conversationActionType="keycite",
            profile=summarize_answer_generation_request.answer_solution_profile.lower(),
            request_headers=request.headers.items(),
            request_cookies=request.cookies,
        ),
        event_name="RASSearch.AI.UserRequestHeaders",
        correlation_id=start_answer_generation_response.conversation_entry_id,
        user_session=cobalt_session,
    )

    send_event(body=header_event, auth_token=auth_token, wait=True)

    dynamo_db.write_progress(
        user_input="N/A",
        user_id=start_answer_generation_response.user_id,
        conversation_id=start_answer_generation_response.conversation_id,
        conversation_entry_id=start_answer_generation_response.conversation_entry_id,
        conversation_action_type=ConversationActionType.KEYCITE,
        ttl=settings.DYNAMODB_TTL,
    )
    send_summarize_task(
        user_id=user_id,
        legacy_id=summarize_answer_generation_request.legacy_id,
        headnote_id=summarize_answer_generation_request.headnote_id,
        answer_solution_profile=summarize_answer_generation_request.answer_solution_profile,
        citing_case_ids=summarize_answer_generation_request.citing_case_ids,
        conversation_id=conversation_id,
        conversation_entry_id=start_answer_generation_response.conversation_entry_id,
        conversation_action_type=ConversationActionType.KEYCITE,
        auth_token=auth_token,
        cobalt_session=cobalt_session,
        route_to_green=True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False,
        user_classification=user_classification,
    )

    return start_answer_generation_response


def get_task_info(task_id):
    """
    return task info for the given task_id
    """
    try:
        task_result = AsyncResult(task_id)
        obj = {}
        if task_result.status == "PENDING":
            # Pending the default state which means not found. Switch to -i to see if the task was an intent task.
            task_result = AsyncResult(f"{task_id}-i")

        if task_result.result is not None:
            obj = JsonUtils.dict_to_json_obj(task_result.result)
        else:
            obj = None

        result = {"task_id": task_id, "task_status": task_result.status, "task_result": obj}
        return result
    except Exception as ex:
        logger.error(f"Exception occurred while getting task info for task_id {task_id}: {ex}")
        return {"task_id": task_id, "task_status": "FAILURE Retrieving", "task_result": str(task_result.result)}


def get_task_metadata(task_id):
    """
    return task info for the given task_id (V2)
    """
    try:
        task_result = AsyncResult(task_id)

        if task_result.status == "PENDING":
            # Pending the default state which means not found. Switch to -i to see if the task was an intent task.
            task_result = AsyncResult(f"{task_id}-i")
        result = task_result.result
        # Revoked tasks can't be assigned
        if task_result.status == "REVOKED":
            result = None
        if result:
            result["task_id"] = task_id
            result["task_status"] = task_result.status

            worker_task = WorkerTask(task_id=task_id, s3_bucket=settings.S3_BUCKET, region=settings.REGION)
            if DEBUG_GROUP_KEY in task_result.result:
                debug_info = worker_task.get_debug_info()
                result[DEBUG_GROUP_KEY] = debug_info.model_dump()

            # Check if the result contains any S3:: references and replace them with actual data
            # We are looking for a structure like:
            # "task_metadata": {
            #   ...
            #   "intermediate_results": {
            #      "agent_thinking": "S3::some_s3_path",
            #   ...
            #   }
            for key, value in result.items():
                # we don't know the name, so, we will check to see if it is a dictionary, DEBUG_GROUP_KEY is
                # a special case above
                if isinstance(value, dict) and key != "DEBUG_GROUP_KEY":
                    for sub_key, sub_value in value.items():
                        # Check if the value is a string starting with "S3::"
                        if isinstance(sub_value, str) and sub_value.startswith("S3::"):
                            value[sub_key] = worker_task.get_intermediate_info(sub_value)
        else:
            result = {"task_id": task_id, "task_status": task_result.status, "task_result": None}
        return result
    except Exception as ex:
        logger.error(f"Exception occurred while getting task info for task_id {task_id}: {ex}", exc_info=True)
        return {"task_id": task_id, "task_status": "FAILURE Retrieving", "task_result": None}


@tracer.wrap()
def create_conversation_task_v3(
        is_new_conversation: bool,
        auth_token: str,
        user_session: dict,
        request: Request,
        user_id: str,
        conversation_id: str,
        user_input: str,
        profile: str,
        product: str,
        title: str,
        conversation_metadata: Optional[ConversationMetadataV2],
        conversation_entry_metadata: Optional[ConversationEntryMetadataV2],
        subscribed_skills: Optional[List[AalpSkill]],
        conversation_type: str,
        additional_user_inputs: Optional[dict] = {},
        overrides: Optional[dict] = {},
        filters: Optional[dict] = {},
        data_retention: DataRetention = Constants.CONV_DATA_RETENTION_DEFAULT,
        is_favorite: bool = False,
        max_followup_date: Optional[int] = None,
        auto_submit_task: bool = True,
        user_classification: str = "unknown",
        static_entry_ids: bool = False
):
    auth_token = auth_token.replace("Bearer ", "")

    remapped_profile = str(remap_profile(profile).lower())
    answer_profile: AnswerProfile = answer_profile_service.get_profile(name=remapped_profile)
    if not answer_profile.has_action_sequence():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Profile must have an action_sequence defined for v3.")
    add_to_current_span("profile", remapped_profile)
    add_trace_context_to_current_span(conversation_id, "", user_session)
    add_to_current_span("conversation_type", conversation_type)

    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False

    metadata = {
        Constants.TASK_MD_AUTO_SUBMIT_TASK: auto_submit_task,
        Constants.TASK_MD_ROUTE_TO_GREEN: route_to_green,
        Constants.TASK_MD_USER_CLASSIFICATION: user_classification,
        Constants.TASK_MD_CONVERSATION_TYPE: conversation_type,
    }

    if Constants.CONTENT_TYPES_OVERRIDE in overrides and len(overrides[Constants.CONTENT_TYPES_OVERRIDE]) > 0:
        metadata[Constants.TASK_MD_CONTENT_TYPES_EXCLUDE] = overrides[Constants.CONTENT_TYPES_OVERRIDE]
    if static_entry_ids:
        metadata[Constants.TASK_MD_STATIC_ENTRY_IDS] = True

    attribute_updates, custom_conversation_metadata = process_conversation_metadata(conversation_metadata)

    start_conversation_response = StartConversationResponse(user_id=user_id, conversation_id=conversation_id)
    if is_new_conversation:
        dynamo_db_v2.write_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            product=product,
            profile=remapped_profile,
            title=title,
            custom_metadata=custom_conversation_metadata,
            data_retention=data_retention,
            is_favorite=is_favorite,
            max_followup_date=max_followup_date,
            attribute_updates=attribute_updates
        )
    else:
        # add/replace attribute_updates with below data
        attribute_updates.update({
            Constants.CONV_META_PRODUCT: product,
            Constants.CONV_META_PROFILE: remapped_profile,
            Constants.CONV_META_TITLE: title,
            Constants.CONV_META_DATA_RETENTION: data_retention.value,
            Constants.CONV_META_LATEST_ENTRY_DATE: int(time.time()),
            Constants.CONV_META_LATEST_ENTRY_STATUS: RetrieveConversationEntryStatuses.IN_PROGRESS.value,
            Constants.CONV_META_IS_FAVORITE: is_favorite,
        })

        dynamo_db_v2.update_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            attribute_updates=attribute_updates,
            custom_metadata_updates=custom_conversation_metadata,
        )

    conversation = dynamo_db_v2.get_conversation(user_id=user_id,
                                                 conversation_id=start_conversation_response.conversation_id,
                                                 include_results=True,
                                                 consistent_read=True)

    # We need to update any conversation entries that were flagged as requiring user input after we get a POST or PUT
    if conversation.conversation_entries:
        for conversation_entry in conversation.conversation_entries:
            if conversation_entry.user_interaction_required:
                dynamo_db_v2.update_conversation_entry(user_id=user_id,
                                                       conversation_id=conversation_id,
                                                       conversation_entry_id=conversation_entry.conversation_entry_id,
                                                       attribute_updates={Constants.USER_INTERACTION_REQUIRED: False})

    start_conversation_response.conversation_metadata = conversation.conversation_metadata

    task_action_sequence = action_sequence_util.get_next_action_sequence(conversation=conversation,
                                                                         user_input=user_input,
                                                                         action_sequences=answer_profile.action_sequence,
                                                                         from_conversation_request=True)

    task_id = str(uuid.uuid4())

    """
    Check if the user_input and additional_user_input are large and if so, replace their values with the s3 path and add 
    to metadata that inputs are large. Large inputs will need to be retrieved from S3 location as to not be passed 
    on the redis task. This is done to improve redis performance and avoid redis task size limits.
    """
    user_input, additional_user_inputs = handle_large_inputs(user_input=user_input,
                                                             additional_user_inputs=additional_user_inputs,
                                                             answer_profile=answer_profile,
                                                             task_id=task_id,
                                                             user_id=user_id,
                                                             conversation_id=conversation_id,
                                                             metadata=metadata,
                                                             data_retention=data_retention)

    conversation_entry_id = send_action_sequence_task(
        task_id=task_id,
        user_id=user_id,
        user_input=user_input,
        additional_user_inputs=additional_user_inputs,
        answer_solution_profile=remapped_profile,
        overrides=overrides,
        filters=filters,
        conversation_id=conversation_id,
        auth_token=auth_token,
        task_action_sequence=task_action_sequence,
        action_sequences=answer_profile.action_sequence,
        user_session=user_session,
        route_to_green=route_to_green,
        meta_data=metadata,
        conversation_entry_metadata=conversation_entry_metadata,
        subscribed_skills=subscribed_skills,
    )

    generate_conversation_event(
        auth_token=auth_token,
        cobalt_session=user_session,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        request=request,
        profile=remapped_profile,
        action_type=ConversationActionType.RAG,
    )

    return start_conversation_response


@tracer.wrap()
def create_conversation_entry_v2(
        is_new_conversation: bool,
        auth_token: str,
        user_session: dict,
        request: Request,
        user_id: str,
        conversation_id: str,
        user_input: str,
        profile: str,
        jurisdictions_override: List[str],
        content_types_override: List[str],
        content_types_exclude: List[str],
        product: str,
        title: str,
        custom_conversation_metadata: ConversationCustomMetadata,
        email_notification: EmailNotification,
        custom_conversation_entry_metadata: ConversationEntryCustomMetadata,
        conversation_type: str,
        run_intent_resolver: bool = True,
        auto_submit_task: bool = True,
        user_classification: str = "unknown",
        data_retention: DataRetention = Constants.CONV_DATA_RETENTION_DEFAULT,
        is_favorite: bool = False,
        max_followup_date: Optional[int] = None,
        static_entry_ids: bool = False,
):
    auth_token = auth_token.replace("Bearer ", "")

    remapped_profile = str(remap_profile(profile).lower())
    answer_profile = answer_profile_service.get_profile(name=remapped_profile)
    add_to_current_span("profile", remapped_profile)

    conversation_entry_id = str(uuid.uuid4())
    start_conversation_response = StartConversationResponse(user_id=user_id, conversation_id=conversation_id,
                                                            conversation_entry_id=conversation_entry_id)
    add_trace_context_to_current_span(conversation_id, start_conversation_response.conversation_entry_id, user_session)

    add_to_current_span("conversation_type", conversation_type)

    create_conversation_received_event(is_new_conversation=is_new_conversation,
                                       user_session=user_session,
                                       conversation_id=conversation_id,
                                       conversation_entry_id=conversation_entry_id,
                                       profile=remapped_profile,
                                       auth_token=auth_token)

    is_initial = conversation_type == Constants.CONV_INITIAL_CONVERSATION_NAME

    # Check for rag entry in cache. If not present, send the task
    is_cached = (dynamo_db_v2.is_conversation_entry_cached(user_input=user_input,
                                                           conversation_action_type=ConversationActionType.RAG,
                                                           answer_profile=answer_profile,
                                                           cache_criteria=jurisdictions_override,
                                                           conversation_type=conversation_type)
                 and is_initial)

    if not is_cached and getattr(answer_profile, "intent_task_enabled", False) and is_initial and run_intent_resolver:
        # Check if there is an intent row cached.
        intent_only_is_cached = dynamo_db_v2.is_conversation_entry_cached(user_input=user_input,
                                                                          conversation_action_type=ConversationActionType.INTENT,
                                                                          answer_profile=answer_profile,
                                                                          cache_criteria=jurisdictions_override,
                                                                          conversation_type=conversation_type)
    else:
        intent_only_is_cached = False

    add_to_current_span("conversation_cache_hit", 1 if is_cached or intent_only_is_cached else 0)

    if is_new_conversation:
        dynamo_db_v2.write_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            product=product,
            profile=remapped_profile,
            title=title,
            custom_metadata=custom_conversation_metadata,
            data_retention=data_retention,
            is_favorite=is_favorite,
            max_followup_date=max_followup_date,
        )
    elif custom_conversation_metadata:
        attribute_updates = {
            Constants.CONV_META_PRODUCT: product,
            Constants.CONV_META_PROFILE: remapped_profile,
            Constants.CONV_META_TITLE: title,
            Constants.CONV_META_DATA_RETENTION: data_retention.value,
            Constants.CONV_META_LATEST_ENTRY_DATE: int(time.time()),
            Constants.CONV_META_LATEST_ENTRY_STATUS: RetrieveConversationEntryStatuses.IN_PROGRESS.value,
            Constants.CONV_META_IS_FAVORITE: is_favorite,
        }
        dynamo_db_v2.update_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            attribute_updates=attribute_updates,
            custom_metadata_updates=custom_conversation_metadata,
        )
    else:
        dynamo_db_v2.update_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            attribute_updates={
                Constants.CONV_META_PRODUCT: product,
                Constants.CONV_META_PROFILE: remapped_profile,
                Constants.CONV_META_TITLE: title,
                Constants.CONV_META_DATA_RETENTION: data_retention.value,
                Constants.CONV_META_LATEST_ENTRY_DATE: int(time.time()),
                Constants.CONV_META_LATEST_ENTRY_STATUS: RetrieveConversationEntryStatuses.IN_PROGRESS.value,
                Constants.CONV_META_IS_FAVORITE: is_favorite,
            }
        )

    dynamo_db_v2.write_conversation_entry(
        user_input=user_input,
        jurisdictions_override=jurisdictions_override,
        content_types_override=content_types_override,
        user_id=user_id,
        profile=remapped_profile,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        conversation_action_type=ConversationActionType.INTENT if run_intent_resolver else ConversationActionType.RAG,
        ttl=settings.DYNAMODB_TTL,
        email_notification=email_notification,
        custom_metadata=custom_conversation_entry_metadata,
    )

    generate_conversation_event(
        auth_token=auth_token,
        cobalt_session=user_session,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        request=request,
        profile=remapped_profile,
        action_type=ConversationActionType.RAG,
    )

    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False

    metadata = {
        Constants.TASK_MD_AUTO_SUBMIT_TASK: auto_submit_task,
        Constants.TASK_MD_ROUTE_TO_GREEN: route_to_green,
        Constants.TASK_MD_USER_CLASSIFICATION: user_classification,
        Constants.TASK_MD_CONVERSATION_TYPE: conversation_type,
    }
    if content_types_exclude and len(content_types_exclude) > 0:
        metadata[Constants.TASK_MD_CONTENT_TYPES_EXCLUDE] = content_types_exclude
    if static_entry_ids:
        metadata[Constants.TASK_MD_STATIC_ENTRY_IDS] = True

    if run_intent_resolver:
        evaluate_intent(is_new_conversation=is_new_conversation,
                        user_id=user_id,
                        user_input=user_input,
                        answer_solution_profile=remapped_profile,
                        jurisdictions_override=jurisdictions_override,
                        content_types_override=content_types_override,
                        conversation_id=conversation_id,
                        conversation_entry_id=conversation_entry_id,
                        conversation_action_type=ConversationActionType.INTENT,
                        auth_token=auth_token,
                        user_session=user_session,
                        route_to_green=route_to_green,
                        meta_data=metadata,
                        is_cached_intent=intent_only_is_cached or is_cached)
    else:
        send_conversation_task_v2(
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=user_input,
            answer_solution_profile=remapped_profile,
            jurisdictions_override=jurisdictions_override,
            content_types_override=content_types_override,
            conversation_id=start_conversation_response.conversation_id,
            conversation_entry_id=start_conversation_response.conversation_entry_id,
            conversation_action_type=ConversationActionType.RAG,
            auth_token=auth_token,
            user_session=user_session,
            route_to_green=route_to_green,
            meta_data=metadata,
            is_cached_conversation=is_cached
        )

    start_conversation_response.conversation_metadata = (
        dynamo_db_v2.get_conversation(user_id=user_id,
                                      conversation_id=start_conversation_response.conversation_id,
                                      include_results=False,
                                      consistent_read=True)
        .conversation_metadata)
    start_conversation_response.conversation_entry_metadata = (
        dynamo_db_v2.get_conversation_entry(user_id=user_id,
                                            conversation_id=start_conversation_response.conversation_id,
                                            conversation_entry_id=start_conversation_response.conversation_entry_id,
                                            consistent_read=True)
        .conversation_entry_metadata)
    return start_conversation_response


def trigger_conversation_task(auth_token, auto_submit_task, cobalt_session, conversation_version,
                              is_new_conversation, request, run_intent_resolver, start_conversation_request,
                              start_conversation_response, user_classification, user_id):
    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False
    answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=start_conversation_request.answer_solution_profile.lower())
    if run_intent_resolver and answer_profile.intent_enabled():
        evaluate_intent(is_new_conversation=is_new_conversation,
                        user_id=user_id,
                        user_input=start_conversation_request.user_input,
                        answer_solution_profile=start_conversation_request.answer_solution_profile,
                        jurisdictions_override=start_conversation_request.jurisdictions_override,
                        content_types_override=start_conversation_request.content_types_override,
                        conversation_id=start_conversation_response.conversation_id,
                        conversation_entry_id=start_conversation_response.conversation_entry_id,
                        conversation_action_type=ConversationActionType.INTENT,
                        auth_token=auth_token,
                        user_session=cobalt_session,
                        route_to_green=route_to_green,
                        meta_data={"auto_submit_task": auto_submit_task,
                                   "route_to_green": route_to_green,
                                   "user_classification": user_classification})
    elif conversation_version == 2:
        send_conversation_task_v2(
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=start_conversation_request.user_input,
            answer_solution_profile=start_conversation_request.answer_solution_profile,
            jurisdictions_override=start_conversation_request.jurisdictions_override,
            content_types_override=start_conversation_request.content_types_override,
            conversation_id=start_conversation_response.conversation_id,
            conversation_entry_id=start_conversation_response.conversation_entry_id,
            conversation_action_type=ConversationActionType.RAG,
            auth_token=auth_token,
            user_session=cobalt_session,
            route_to_green=route_to_green,
            meta_data={"auto_submit_task": auto_submit_task,
                       "route_to_green": route_to_green,
                       "user_classification": user_classification}
        )
    else:
        send_conversation_task(
            is_new_conversation=is_new_conversation,
            user_id=user_id,
            user_input=start_conversation_request.user_input,
            answer_solution_profile=start_conversation_request.answer_solution_profile,
            jurisdictions_override=start_conversation_request.jurisdictions_override,
            content_types_override=start_conversation_request.content_types_override,
            conversation_id=start_conversation_response.conversation_id,
            conversation_entry_id=start_conversation_response.conversation_entry_id,
            conversation_action_type=ConversationActionType.RAG,
            auth_token=auth_token,
            user_session=cobalt_session,
            route_to_green=route_to_green,
            user_classification=user_classification,
        )


def create_conversation_received_event(is_new_conversation: bool,
                                       user_session: UserSession,
                                       conversation_id: str,
                                       conversation_entry_id: str,
                                       profile: str,
                                       auth_token: str,
                                       jurisdictions_override: List[str] = None):
    event = ConversationEventV2(
        properties=ConversationReceivedProperties(conversationId=conversation_id,
                                                  conversationEntryId=conversation_entry_id,
                                                  conversationActionType=ConversationActionType.RAG,
                                                  profile=profile.lower(),
                                                  jurisdictions_override=jurisdictions_override),
        conversation_type=ConversationType.START_CONVERSATION_RECEIVED if is_new_conversation else ConversationType.ADD_CONVERSATION_RECEIVED,
        conversation_entry_id=conversation_entry_id,
        user_session=user_session
    )

    send_event(body=event, auth_token=auth_token, wait=True)


def generate_conversation_event(auth_token: str,
                                cobalt_session: dict,
                                conversation_id: str,
                                conversation_entry_id: Optional[str],
                                request: Request,
                                profile: str,
                                action_type: ConversationActionType):
    header_event = GenericEventV2(
        properties=UserRequestHeaderProperties(
            conversationId=conversation_id,
            conversationEntryId=conversation_entry_id,
            conversationActionType=action_type,
            profile=profile.lower(),
            request_headers=request.headers.items(),
            request_cookies=request.cookies,
        ),
        event_name="RASSearch.AI.UserRequestHeaders.V2",
        correlation_id=conversation_entry_id,
        user_session=cobalt_session,
    )
    send_event(body=header_event, auth_token=auth_token, wait=True)


def append_disclaimer(conversation_entry,
                      profile_name: str):
    if profile_name is not None:
        if conversation_entry.result is None:
            return

        answer_profile = answer_profile_service.get_profile(name=profile_name)
        include_disclaimer = getattr(answer_profile, Constants.LLM_AI_INCLUDE_DISCLAIMER_FLAG, True)
        if not include_disclaimer:
            return

        disclaimer_constant = getattr(answer_profile, Constants.LLM_DISCLAIMER_CONSTANT, "LLM_AI_DEFAULT_DISCLAIMER")
        disclaimer_override_text = getattr(answer_profile, Constants.LLM_DISCLAIMER_OVERRIDE_TEXT_KEY, None)
        system_output_key = getattr(answer_profile, Constants.CONV_SYSTEM_OUTPUT_KEY, "response")
        disclaimer_use_html = getattr(answer_profile, Constants.LLM_DISCLAIMER_USE_HTML, False)

        logger.info(f"Include disclaimer: {include_disclaimer} for profile: {profile_name} :: "
                    f" System output key: {system_output_key} :: disclaimer_constant: {disclaimer_constant}")

        system_output = conversation_entry.result.system_output
        disclaimer_text = disclaimer_override_text if disclaimer_override_text is not None else getattr(Constants,
                                                                                                        disclaimer_constant)

        if system_output_key in system_output:
            if disclaimer_text not in system_output[system_output_key]:
                if disclaimer_use_html:
                    system_output[system_output_key] += "<p>" + disclaimer_text.replace("\n", "<br/>") + "</p>"
                else:
                    system_output[system_output_key] += disclaimer_text
        else:
            system_output['disclaimer'] = disclaimer_text
    else:
        logger.info("Profile name is None. Skipping appending disclaimer.")


def get_conversation_v2(user_id: str,
                        conversation_id: str,
                        request: Request,
                        include_intermediate_results: bool = False,
                        from_v2_endpoint: bool = True) -> ConversationV2:
    try:
        user_session = UserSession(request)
        add_trace_context_to_current_span(conversation_id, "", user_session.get_session_info())
        conversation: ConversationV2 = dynamo_db_v2.get_conversation(
            user_id=user_id,
            conversation_id=conversation_id,
            include_intermediate_results=include_intermediate_results,
            include_rag_pipeline_output=False
        )
        if conversation is None:
            raise DynamoDBNotFoundException(part_key=user_id, sort_key=conversation_id)
        conversation.conversation_entries.sort(key=lambda x: x.timestamp, reverse=True)

        profile_name = None
        if conversation.conversation_metadata and conversation.conversation_metadata.profile:
            profile_name = conversation.conversation_metadata.profile
            add_to_current_span("profile", profile_name)

        for conversation_entry in conversation.conversation_entries:
            if not conversation_entry.task_metadata:
                task_metadata = get_task_metadata(conversation_entry.conversation_entry_id)

                if task_metadata:
                    conversation_entry.task_metadata = task_metadata
            else:
                task_metadata = conversation_entry.task_metadata

            conversation_entry.percent_complete = task_metadata.get("percent_complete", None)

            # For V2, user_interaction_required should not be output
            if from_v2_endpoint:
                if conversation_entry.user_interaction_required is not None:
                    conversation_entry.user_interaction_required = None
                if conversation_entry.additional_user_inputs_store_type is not None:
                    conversation_entry.additional_user_inputs_store_type = None

            if conversation_entry.conversation_action_type == ConversationActionType.RAG:
                append_disclaimer(conversation_entry=conversation_entry, profile_name=profile_name)

        set_span_http_code(status.HTTP_200_OK)
        return conversation
    except HTTPException as http_exception:
        raise http_exception
    except DynamoDBNotFoundException:
        raise_http_error(status.HTTP_404_NOT_FOUND, f"No conversation details found for user_id : {user_id} and "
                                                    f"conversation_id : {conversation_id}.")
    except Exception as e:
        logger.error(e)
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e.__str__())


def get_action_sequences(profile: str) -> Optional[List[ActionSequence]]:
    action_sequences: Optional[List[ActionSequence]] = None
    answer_profile = answer_profile_service.get_profile(name=profile.lower())

    action_sequence = getattr(answer_profile, "action_sequence", None)
    if not action_sequence or isinstance(action_sequence, FieldInfo):
        return action_sequences
    else:
        action_sequences = answer_profile.action_sequence
    return action_sequences


def get_latest_action_sequence_task(conversation: ConversationV3, action_sequences: List[ActionSequence]) \
        -> Optional[ActionSequenceTask]:
    action_sequence_task: Optional[ActionSequenceTask] = None
    latest_conversation_entry: Optional[ConversationEntryV3] = action_sequence_util.get_most_recent_conversation_entry(
        conversation=conversation
    )
    if latest_conversation_entry:
        current_action_sequence: Optional[ActionSequence] = action_sequence_util.get_current_action_sequence(
            conversation_entry=latest_conversation_entry,
            action_sequences=action_sequences
        )
        if current_action_sequence:
            action_sequence_task = ActionSequenceTask(action_type=current_action_sequence.action.value,
                                                      action_status=latest_conversation_entry.status.value,
                                                      conversation_entry_id=latest_conversation_entry.conversation_entry_id,
                                                      user_interaction_required=latest_conversation_entry.user_interaction_required)
    return action_sequence_task


def check_and_store_large_input(
        user_input: str,
        user_id: str,
        conversation_id: str,
        task_id: str,
        metadata: dict,
        data_retention: DataRetention = DataRetention.THREE_HUNDRED_SIXTY_FIVE_DAY
) -> str:
    user_input_size = getsizeof(user_input)
    if user_input_size >= Constants.DYNAMO_DB_MAX_FIELD_SIZE:
        user_input = dynamo_db_v2.store_user_input(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_entry_id=task_id,
            user_input=user_input.encode("utf-8"),
            data_retention=data_retention,
        )
        metadata[Constants.CONV_USER_INPUT_STORE_TYPE] = ConversationEntryUserInputStoreType.S3.value
    return user_input


def check_and_store_large_additional_input(
        additional_user_inputs: dict,
        user_id: str,
        conversation_id: str,
        task_id: str,
        metadata: dict,
        data_retention: DataRetention = DataRetention.THREE_HUNDRED_SIXTY_FIVE_DAY
) -> dict:
    addtl_user_input_str: str = json.dumps(additional_user_inputs)
    additional_user_input_size = getsizeof(addtl_user_input_str)
    if additional_user_input_size >= Constants.DYNAMO_DB_MAX_FIELD_SIZE:
        additional_user_inputs = dynamo_db_v2.store_additional_user_input(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_entry_id=task_id,
            additional_user_input=addtl_user_input_str.encode("utf-8"),
            data_retention=data_retention,
        )
        metadata[Constants.CONV_ADDITIONAL_USER_INPUTS_STORE_TYPE] = ConversationEntryUserInputStoreType.S3.value
    return additional_user_inputs


def handle_large_inputs(
        user_input: str,
        additional_user_inputs: dict,
        answer_profile: AnswerProfile,
        user_id: str,
        conversation_id: str,
        task_id: str,
        metadata: Optional[dict] = None,
        data_retention: DataRetention = DataRetention.THREE_HUNDRED_SIXTY_FIVE_DAY
) -> Tuple[str, dict]:
    if hasattr(answer_profile, Constants.LARGE_INPUT_REDIS_TASK_BYPASS) \
            and getattr(answer_profile, Constants.LARGE_INPUT_REDIS_TASK_BYPASS):
        user_input = check_and_store_large_input(
            user_input=user_input,
            user_id=user_id,
            conversation_id=conversation_id,
            task_id=task_id,
            metadata=metadata,
            data_retention=data_retention)
        additional_user_inputs = check_and_store_large_additional_input(
            additional_user_inputs=additional_user_inputs,
            user_id=user_id,
            conversation_id=conversation_id,
            task_id=task_id,
            metadata=metadata,
            data_retention=data_retention)
    return user_input, additional_user_inputs
