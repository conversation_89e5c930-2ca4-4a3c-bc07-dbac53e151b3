import os

from ddtrace import tracer
from fastapi import Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from gcs_utils import authorization
from gcs_utils.authorization import HTTPAuthorizationCredentials as AuthorizationCredentials
from raslogger import LoggingFactory
from datadog_utils.utils.conversation_trace_utils import add_to_root_span
from conversation_core.shared.utils.version_info import get_non_rag_version_info
from app.config.settings import Settings, get_settings
from fastapi.exceptions import HTTPException
from gcs_utils.authorization import HTTPException as HttpAuthException

bearer_scheme = HTTPBearer()
logger = LoggingFactory.get_logger(__name__)
settings: Settings = get_settings()
required_rights = ["MANAGE_RAS_SEARCH_AI_CONVERSATION"]


@tracer.wrap(service=os.getenv("DD_SERVICE"))
async def authorize_without_rights(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    try:
        return await authorization.authorize_without_rights(
            credentials=AuthorizationCredentials(scheme=credentials.scheme, token=credentials.credentials),
            env=settings.GCS_ENVIRONMENT,
        )
    except HttpAuthException as ex:
        raise HTTPException(status_code=ex.status_code, detail=ex.detail)


@tracer.wrap(service=os.getenv("DD_SERVICE"))
async def authorize_with_rights(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    try:
        return await authorization.authorize_with_rights(
            credentials=AuthorizationCredentials(scheme=credentials.scheme, token=credentials.credentials),
            env=settings.GCS_ENVIRONMENT,
            required_auth_rights=required_rights,
        )
    except HttpAuthException as ex:
        raise HTTPException(status_code=ex.status_code, detail=ex.detail)
