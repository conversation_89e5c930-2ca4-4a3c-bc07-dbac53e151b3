import uuid
from typing import Annotated, Optional, List

from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.models.answer_profile import AnswerProfile, ActionSequence
from conversation_core.shared.models.v2.conversation import Conversation as ConversationV2, \
    StartConversationResponse
from conversation_core.shared.models.v3.conversation import (StartConversationRequestV3, ConversationV3,
                                                             ActionSequenceTask, conversation_v2_to_v3)
from conversation_core.shared.models.v3.conversation_entry import ConversationEntryV3, conversation_entry_v2_to_v3
from conversation_core.shared.utils.trace import add_to_current_span, add_trace_context_to_current_span
from fastapi import APIRouter, Depends, Request, Path, Body, Query
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from app.api.constants.doc import CREATE_CONVERSATION_EXAMPLES_V3, \
    CREATE_CONVERSATION_RESPONSES_V3, CONTINUE_CONVERSATION_EXAMPLES_V3, GET_CONVERSATION_ENTRY_V3_RESPONSE, \
    GET_CONVERSATION_V3_RESPONSES
from app.config.settings import Settings
from config import settings
from services.chat_profile_service import answer_profile_service
from services.conversation_creation_service import (get_conversation_v2, append_disclaimer, get_task_metadata,
                                                    get_action_sequences, get_latest_action_sequence_task)
from services.conversation_creation_service import start_or_continue_conversation_v3
from utils.session_utils import set_user_session
from utils.validation_utils import validate_conversation_v3_request, validate_conversation_for_followup, \
    check_product_name_header, check_product_view_header, check_asset_id_header, check_user_type_header

logger = LoggingFactory.get_logger(__name__)
conversation_router_v3 = APIRouter(prefix="/api/v3/conversation", tags=["V3", "Upcoming"])
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]


@conversation_router_v3.post(
    path="/{user_id}/entry",
    status_code=status.HTTP_200_OK,
    response_model=StartConversationResponse,
    dependencies=gcs_auth_rights,
    responses=CREATE_CONVERSATION_RESPONSES_V3,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    description="Start a new conversation for a user. "
                "Minimum required fields in request body are user_input and answer_solution_profile. "
                "Product name and view headers are required. "
                "Overrides and filters are optional.",
    # tags=["Start or Continue Conversation"]
)
def start_new_conversation_v3(
        start_conversation_request: Annotated[
            StartConversationRequestV3,
            Body(..., openapi_examples=CREATE_CONVERSATION_EXAMPLES_V3),
        ],
        request: Request,
        user_id: Annotated[str, Path(
            description="User ID to start a conversation for.",
            example="default-user-id"
        )],
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        x_tr_asset_id: Annotated[str, Depends(check_asset_id_header)],
        x_tr_user_type: Annotated[str, Depends(check_user_type_header)],
        validate_request: StartConversationRequestV3 = Depends(validate_conversation_v3_request),
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    set_user_session(request, settings)
    logger.info(f"Starting new V3 conversation for user {user_id} (of type {x_tr_user_type}), "
                f"from view '{x_tr_product_view}' "
                f"in product '{x_tr_product_name}' "
                f"with asset id '{x_tr_asset_id}'")
    return start_or_continue_conversation_v3(request=request,
                                             user_id=user_id,
                                             start_conversation_request=validate_request,
                                             is_new_conversation=True,
                                             conversation_id=str(uuid.uuid4()),
                                             conversation_type=Constants.CONV_INITIAL_CONVERSATION_NAME,
                                             auth_token=auth_token,
                                             static_entry_ids=True)


@conversation_router_v3.put(
    "/{user_id}/{conversation_id}/entry",
    status_code=status.HTTP_200_OK,
    response_model=StartConversationResponse,
    dependencies=gcs_auth_rights,
    responses=CREATE_CONVERSATION_RESPONSES_V3,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    description="Continue a conversation for a user. "
                "Minimum required fields in request body are user_input and answer_solution_profile. "
                "Product name and view headers are required. "
                "Overrides and filters are optional.",
    # tags=["Start or Continue Conversation"]
)
def continue_conversation_v3(
        start_conversation_request: Annotated[
            StartConversationRequestV3,
            Body(..., openapi_examples=CONTINUE_CONVERSATION_EXAMPLES_V3),
        ],
        request: Request,
        user_id: Annotated[str, Path(
            description="User ID to start a conversation for.",
            example="default-user-id"
        )],
        conversation_id: Annotated[str, Path(
            description="Conversation ID of existing conversation to continue.",
            example="c0000000-0000-0000-0000-000000000000"
        )],
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        x_tr_asset_id: Annotated[str, Depends(check_asset_id_header)],
        x_tr_user_type: Annotated[str, Depends(check_user_type_header)],
        validate_request: StartConversationRequestV3 = Depends(validate_conversation_v3_request),
        auth_token: str = Depends(gcs_auth.authorize_with_rights)
):
    set_user_session(request, settings)
    logger.info(f"Continuing conversation {conversation_id} for user {user_id} (of type {x_tr_user_type}), "
                f"from view '{x_tr_product_view}' "
                f"in product '{x_tr_product_name}' "
                f"with asset id '{x_tr_asset_id}'")
    answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=validate_request.answer_solution_profile)
    conversation: Optional[ConversationV2] = dynamo_db_v2.get_conversation(
        user_id=user_id,
        conversation_id=conversation_id,
        include_intermediate_results=False,
    )

    validate_conversation_for_followup(conversation=conversation,
                                       answer_profile=answer_profile,
                                       from_v3_endpoint=True)
    return start_or_continue_conversation_v3(request=request,
                                             user_id=user_id,
                                             start_conversation_request=validate_request,
                                             is_new_conversation=False,
                                             conversation_id=conversation_id,
                                             conversation_type=Constants.CONV_FOLLOWUP_CONVERSATION_NAME,
                                             auth_token=auth_token,
                                             static_entry_ids=True)


@conversation_router_v3.get(
    "/{user_id}/{conversation_id}",
    status_code=status.HTTP_200_OK,
    response_model=ConversationV3,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    responses=GET_CONVERSATION_V3_RESPONSES,
    # tags=["Conversation Actions"]
)
def get_conversation_v3(
        request: Request,
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        x_tr_asset_id: Annotated[str, Depends(check_asset_id_header)],
        x_tr_user_type: Annotated[str, Depends(check_user_type_header)],
        user_id: Annotated[str, Path(description="User ID to get conversation for.")],
        conversation_id: Annotated[str, Path(description="Conversation ID to get.")],
        include_intermediate_results: Annotated[
            bool,
            Query(description="whether or not to include the intermediate results")] = False,
        auth_token: str = Depends(gcs_auth.authorize_with_rights)):
    logger.info(f"Getting conversation {conversation_id} for user {user_id} (of type {x_tr_user_type})"
                f"from view '{x_tr_product_view}' "
                f"in product '{x_tr_product_name}' "
                f"with asset id '{x_tr_asset_id}'")
    user_session = set_user_session(request, settings)
    add_trace_context_to_current_span(conversation_id, "", user_session.get_session_info())

    conversation_v2: ConversationV2 = get_conversation_v2(request=request,
                                                          user_id=user_id,
                                                          conversation_id=conversation_id,
                                                          include_intermediate_results=include_intermediate_results,
                                                          from_v2_endpoint=False)
    conversation_v3: ConversationV3 = conversation_v2_to_v3(conversation_v2,
                                                            Constants.QUERY_ANNOTATION_TYPES,
                                                            Constants.INTENT_CATEGORY_ANNOTATION_TYPES,
                                                            Constants.OUT_OF_SCOPE_INTENT_CATEGORY_ANNOTATION_TYPES,
                                                            Constants.JURISDICTION_ANNOTATION_TYPES)

    profile_name = None
    if conversation_v3.conversation_metadata and conversation_v3.conversation_metadata.profile:
        profile_name = conversation_v3.conversation_metadata.profile
        add_to_current_span("profile", profile_name)

    if conversation_v3.conversation_metadata and profile_name:
        action_sequences: Optional[List[ActionSequence]] = get_action_sequences(profile=profile_name)
        latest_action_sequence_task: Optional[ActionSequenceTask] = None
        if action_sequences:
            conversation_v3.conversation_metadata.conversation_action_sequence = action_sequences
            latest_action_sequence_task = get_latest_action_sequence_task(conversation=conversation_v3,
                                                                          action_sequences=action_sequences)
        if latest_action_sequence_task:
            conversation_v3.conversation_metadata.current_action_sequence_task = latest_action_sequence_task

    return conversation_v3.dict(
        exclude={"conversation_metadata": {"conversation_action_sequence": {"__all__": ["blocks", "nudges"]}}})


@conversation_router_v3.get(
    "/{user_id}/{conversation_id}/{conversation_entry_id}",
    status_code=status.HTTP_200_OK,
    response_model=ConversationEntryV3,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    responses=GET_CONVERSATION_ENTRY_V3_RESPONSE,
    # tags=["Conversation Actions"]
)
def get_conversation_entry_v3(
        request: Request,
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        x_tr_asset_id: Annotated[str, Depends(check_asset_id_header)],
        x_tr_user_type: Annotated[str, Depends(check_user_type_header)],
        user_id: Annotated[str, Path(description="User ID to get conversation for.")],
        conversation_id: Annotated[str, Path(description="Conversation ID to get.")],
        conversation_entry_id: Annotated[str, Path(description="Conversation Entry ID to get.")],
        auth_token: str = Depends(gcs_auth.authorize_with_rights)):
    logger.info(f"Getting conversation entry {conversation_entry_id} from conversation {conversation_id} "
                f"from view '{x_tr_product_view}' "
                f"in product '{x_tr_product_name}' "
                f"with asset id '{x_tr_asset_id}'"
                f"for user {user_id} (of type {x_tr_user_type})")
    set_user_session(request, settings)
    conversation_entry_v2 = dynamo_db_v2.get_conversation_entry(
        user_id=user_id, conversation_id=conversation_id, conversation_entry_id=conversation_entry_id,
        include_rag_pipeline_output=False
    )
    conversation_entry_v2.task_metadata = get_task_metadata(conversation_entry_id)
    conversation_entry_v2.percent_complete = conversation_entry_v2.task_metadata.get("percent_complete", None)
    profile_name = None
    if hasattr(conversation_entry_v2, 'profile') and conversation_entry_v2.profile:
        profile_name = conversation_entry_v2.profile
    append_disclaimer(conversation_entry=conversation_entry_v2, profile_name=profile_name)
    conversation_entry_v3 = conversation_entry_v2_to_v3(
        conversation_entry_v2,
        Constants.QUERY_ANNOTATION_TYPES,
        Constants.INTENT_CATEGORY_ANNOTATION_TYPES,
        Constants.OUT_OF_SCOPE_INTENT_CATEGORY_ANNOTATION_TYPES,
        Constants.JURISDICTION_ANNOTATION_TYPES)
    return conversation_entry_v3
