import os
from http import HTTPStatus

from datadog_utils.utils import ras_tracer
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from raslogger import LoggingFactory
from starlette import status

logger = LoggingFactory.get_logger(__name__)
health_check_router = APIRouter(prefix="/actuator/health")

pod_name = os.getenv("HOSTNAME", "unknown")

@health_check_router.get("/liveness", status_code=HTTPStatus.OK, include_in_schema=False)
async def liveness():
    logger.debug("Liveness endpoint has been called.")
    http_status = status.HTTP_200_OK
    return_status = "UP"
    message = "is healthy."
    return JSONResponse(
        status_code=http_status,
        content={
            "status": return_status,
            "pod": pod_name,
            "message": f"Container {message}",
        },
    )


@health_check_router.get("/readiness", include_in_schema=False)
async def readiness():
    logger.debug("Readiness endpoint has been called.")
    return_status = "UP"
    http_status = status.HTTP_200_OK
    message = "is ready."
    return JSONResponse(
        status_code=http_status,
        content={
            "status": return_status,
            "pod": pod_name,
            "message": f"Container {message}",
        },
    )


@health_check_router.get("", status_code=HTTPStatus.OK, include_in_schema=False)
async def actuator():
    logger.debug("Actuator endpoint has been called.")
    return {"status": "UP", " groups": ["liveness", "readiness"]}
