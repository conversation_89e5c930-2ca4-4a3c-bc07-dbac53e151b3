from typing import Annotated

from conversation_core.shared.dynamo_helper_v2 import ConversationD<PERSON> as ConversationDBV2
from fastapi import APIRouter, Depends, Request
from fastapi import Path, Query
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from api.constants.doc import CONVERSATION_STATISTICS_RESPONSES
from api.endpoints.conversation_router import raise_http_error, log_version_info
from api.models.conversations.user_session_model import UserSession
from config import settings
from config.settings import Settings
from utils.validation_utils import check_product_view_header, check_product_name_header

logger = LoggingFactory.get_logger(__name__)
conversation_statistics_router = APIRouter(prefix="/api/v1/conversation-statistics", tags=["V1"])
conversation_statistics_router_v2 = APIRouter(prefix="/api/v2/conversation-statistics", tags=["V2"])
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]


@conversation_statistics_router.get("/{user_id}", status_code=status.HTTP_200_OK, dependencies=gcs_auth_rights,
                                    deprecated=True, tags=["Deprecated"])
async def get_statistics(user_id: str, request: Request, latest_entry_gte: int = 0):
    additional_params = dict(request.query_params)
    additional_params.pop("latest_entry_gte", None)
    if additional_params:
        raise raise_http_error(status.HTTP_400_BAD_REQUEST, f"Unknown query parameters: {set(additional_params)}")
    return dynamo_db_v2.get_conversation_statistics(user_id=user_id, latest_entry_date_gte=latest_entry_gte)


@conversation_statistics_router_v2.get(path="/{user_id}",
                                       status_code=status.HTTP_200_OK,
                                       responses=CONVERSATION_STATISTICS_RESPONSES,
                                       dependencies=gcs_auth_rights,
                                       tags=["Conversation Actions"])
async def get_statistics_v2(
        user_id: Annotated[str, Path(
            description="User ID using the API, any string value is valid."
        )],
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        request: Request,
        latest_entry_gte: Annotated[int, Query(
            description="Optionally set how far back to look for the conversations latest entry date, epoch timestamp. "
                        "Default checks conversations that have had an entry in the last 15 minutes.",
            example=1723734919
        )] = 0):
    logger.info(f"get_statistics_v2: user_id={user_id}, latest_entry_gte={latest_entry_gte}, "
                f"x_tr_product_name={x_tr_product_name}, x_tr_product_view={x_tr_product_view}")
    additional_params = dict(request.query_params)
    additional_params.pop("latest_entry_gte", None)
    if additional_params:
        raise raise_http_error(status.HTTP_400_BAD_REQUEST, f"Unknown query parameters: {set(additional_params)}")
    return dynamo_db_v2.get_conversation_statistics_v2(user_id=user_id, latest_entry_date_gte=latest_entry_gte)
