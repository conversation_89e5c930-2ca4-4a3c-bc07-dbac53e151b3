import time
import uuid
from typing import Optional, Annotated

from celery.result import <PERSON><PERSON><PERSON><PERSON><PERSON>
from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper import ConversationDB
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException, ConversationDB as ConversationDBV2
from conversation_core.shared.enums import DataRetention
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.conversation import Conversation
from conversation_core.shared.models.conversation_metadata import ConversationListMetadata
from conversation_core.shared.models.v2.conversation import Conversation as ConversationV2, \
    ConversationMetadata as ConversationMetadataV2, CustomMetadata as ConversationCustomMetadata, \
    GetConversationListResponse, StartConversationResponse, StartConversationRequest
from conversation_core.shared.models.v2.conversation_entry import ConversationEntry as ConversationEntryV2, \
    ConversationEntryMetadata as ConversationEntryMetadataV2, CustomMetadata as ConversationEntryCustomMetadata, \
    EmailNotification
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from conversation_core.shared.utils.trace import add_to_current_span, add_trace_context_to_current_span
from conversation_core.shared.utils.version_info import get_non_rag_version_info
from ddtrace import tracer
from fastapi import APIRouter, Depends, Request, Header, HTTPException, Body, Path, Query
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from api.models.conversations.update_metadata_models import UpdateConversationMetadata, UpdateConversationEntryMetadata
from api.models.conversations.user_session_model import UserSession
from app.api.constants.doc import USER_ID_DESCRIPTION, CREATE_CONVERSATION_DESCRIPTION, CREATE_CONVERSATION_SUMMARY, \
    CREATE_CONVERSATION_RESPONSES, CREATE_CONVERSATION_EXAMPLES, GET_PROFILES_RESPONSES, \
    UPDATE_CONVERSATION_METADATA_EXAMPLES, UPDATE_CONVERSATION_ENTRY_METADATA_EXAMPLES, GET_CONVERSATION_LIST_RESPONSE, \
    GET_CONVERSATION_V2_RESPONSES
from app.api.exceptions.conversation_exceptions import DeleteConversationException, ConversationNotFoundException, \
    UpdateConversationException
from app.api.models.conversations.header_model import ConversationHeaders
from app.api.models.conversations.operation_status import OperationStatus
from app.api.models.conversations.retrieve_answer_models import (
    RetrieveConversationEntryResponse,
    RetrieveConversationEntryStatuses,
)
from app.api.models.conversations.submit_answer_models import (
    StartAnswerGenerationResponse,
    StartAnswerGenerationRequest,
    SummarizeAnswerGenerationRequest,
)
from app.config.settings import Settings
from config import settings
from services.chat_profile_service import answer_profile_service
from services.conversation_creation_service import create_summarize_conversation_entry_v2, get_task_metadata, \
    append_disclaimer, prevent_metadata_update, start_or_continue_conversation_v2, get_task_info, \
    create_conversation_entry, get_conversation_v2, process_conversation_metadata
from utils.span_utils import set_span_http_code, raise_http_error, add_user_classification_to_span
from utils.validation_utils import validate_conversation_request, validate_custom_field_in_metadata, \
    validate_custom_field_in_entry_metadata, get_original_profile, \
    validate_conversation_for_followup, check_product_view_header, check_product_name_header

logger = LoggingFactory.get_logger(__name__)
conversation_router = APIRouter(prefix="/api/v1/conversation", tags=["V1"])
conversation_api_v1 = APIRouter(prefix="/api/v1", tags=["V1"])
conversation_router_v2 = APIRouter(prefix="/api/v2/conversation", tags=["V2"])
conversation_api_v2 = APIRouter(prefix="/api/v2", tags=["V2"])
settings: Settings = settings.get_settings()
dynamo_db = ConversationDB(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]


def log_version_info():
    version_info = get_non_rag_version_info()
    add_to_current_span("version_info.app_version", version_info.get("app_version"))
    add_to_current_span("version_info.python_version", version_info.get("python_version"))


@conversation_router.get(
    "/answer-solution-profiles", status_code=status.HTTP_200_OK, dependencies=gcs_auth_rights,
    deprecated=True, tags=["Deprecated"]
)
@conversation_router_v2.get(
    path="/answer-solution-profiles",
    status_code=status.HTTP_200_OK,
    dependencies=gcs_auth_rights,
    responses=GET_PROFILES_RESPONSES,
    tags=["Developer Endpoints"]
)
async def get_answer_solution_profiles(profile_name: Annotated[str, None] = None):
    if profile_name is not None:
        profile_names = [profile_name]
    else:
        profile_names = answer_profile_service.all_profile_names()

    profile_list = []
    for name in profile_names:
        try:
            log_version_info()
            answer_profile = answer_profile_service.get_profile(name)
            profile_list.append(answer_profile)
        except (NameError, ProfileNotFoundException) as e:
            logger.error(f'Profile name "{profile_name}" not found: {e}')
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f'Profile name "{profile_name}" not found.',
            )
        except Exception as e:
            logger.error(f"Error while getting profile {name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f'Error while getting profile {name}',
            )
    return profile_list


@conversation_router.delete("/{user_id}/{conversation_id}", status_code=status.HTTP_200_OK,
                            dependencies=gcs_auth_rights, deprecated=True, tags=["Deprecated"])
@conversation_router_v2.delete("/{user_id}/{conversation_id}", status_code=status.HTTP_200_OK,
                               dependencies=gcs_auth_rights, tags=["Conversation Actions"])
async def delete_conversation(user_id: str, conversation_id: str, request: Request):
    user_session = UserSession(request=request)
    add_trace_context_to_current_span(conversation_id, "", user_session.get_session_info())
    delete_response = dynamo_db.delete_conversations(user_id=user_id, conversation_id=conversation_id)
    if delete_response != OperationStatus.DELETE_CONVERSATION_SUCCESS:
        raise DeleteConversationException(delete_response)

    return {
        "status": f"Deleted conversation(s) with conversationId {conversation_id} for userId {user_id} successfully!"
    }


# ================= v2 Endpoints =================

@conversation_router_v2.put(
    "/{user_id}/{conversation_id}/{conversation_entry_id}/cancel",
    status_code=status.HTTP_200_OK,
    response_model=ConversationEntryV2,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Conversation Actions"]
)
async def cancel_conversation_entry(
        user_id: str, conversation_id: str, conversation_entry_id: str
):
    try:
        dynamo_db_v2.update_conversation_entry(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            attribute_updates={Constants.CONV_STATUS: RetrieveConversationEntryStatuses.CANCELLING.value}
        )
        AsyncResult(id=conversation_entry_id).revoke()
        return dynamo_db_v2.get_conversation_entry(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id)
    except DynamoDBNotFoundException:
        raise_http_error(status.HTTP_404_NOT_FOUND, f"Could not find conversation entry for user_id: '{user_id}'")
    except Exception as e:
        logger.error(e)
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e.__str__())


@conversation_api_v2.post(
    "/summarize/{user_id}/keycite",
    status_code=status.HTTP_200_OK,
    response_model=StartConversationResponse,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Non-Chat Conversation Actions"]
)
async def summarize_conversation_key_cite(
        user_id: str,
        summarize_answer_generation_request: SummarizeAnswerGenerationRequest,
        request: Request,
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    try:
        headnote_id = summarize_answer_generation_request.headnote_id
        legacy_id = summarize_answer_generation_request.legacy_id
        citing_case_ids = summarize_answer_generation_request.citing_case_ids

        user_session = UserSession(request)
        user_classification = add_user_classification_to_span(user_session.get_session_info())

        conversation_metadata = (ConversationMetadataV2()
                                 if summarize_answer_generation_request.conversation_metadata is None
                                 else summarize_answer_generation_request.conversation_metadata)
        conversation_entry_metadata = (ConversationEntryMetadataV2()
                                       if summarize_answer_generation_request.conversation_entry_metadata is None
                                       else summarize_answer_generation_request.conversation_entry_metadata)
        # Setting up Email notification properties such as sent_email and result_viewed to their default values (False).
        if conversation_entry_metadata.email_notification:
            conversation_entry_metadata.email_notification.email_sent = False
        if conversation_entry_metadata.custom and conversation_entry_metadata.custom.result_viewed:
            conversation_entry_metadata.custom.result_viewed.is_result_viewed = False

        start_conversation_response: StartConversationResponse = create_summarize_conversation_entry_v2(
            request=request,
            auth_token=auth_token,
            cobalt_session=user_session.get_session_info(),
            user_id=user_id,
            headnote_id=headnote_id,
            legacy_id=legacy_id,
            citing_case_ids=citing_case_ids,
            profile=summarize_answer_generation_request.answer_solution_profile,
            product=conversation_metadata.product,
            conversation_metadata=conversation_metadata,
            conversation_entry_metadata=conversation_entry_metadata,
            user_classification=user_classification,
            data_retention=(DataRetention(conversation_metadata.data_retention)
                            if conversation_metadata.data_retention is not None
                            else Constants.CONV_DATA_RETENTION_DEFAULT),
        )

        add_trace_context_to_current_span(start_conversation_response.conversation_id,
                                          start_conversation_response.conversation_entry_id,
                                          user_session.get_session_info())

        set_span_http_code(status.HTTP_200_OK)
        return start_conversation_response
    except HTTPException as http_exception:
        raise http_exception
    except Exception as e:
        logger.error(e)
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e.__str__())


@conversation_router_v2.get(
    "-list/{user_id}",
    response_model=GetConversationListResponse,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    responses=GET_CONVERSATION_LIST_RESPONSE,
    tags=["Conversation Actions"]
)
async def get_conversation_list(
        user_id: str,
        request: Request,
        x_tr_product_name: Annotated[str, Depends(check_product_name_header)],
        x_tr_product_view: Annotated[str, Depends(check_product_view_header)],
        limit: Annotated[
            Optional[int],
            Query(..., description="Limit of conversations to return (limit is set before filtering)")] = Constants.CONV_PAGE_LIMIT_DEFAULT,
        is_favorite: Annotated[
            Optional[bool],
            Query(..., description="favorite conversation filter")] = None,
        ascending_order: Annotated[
            Optional[bool],
            Query(..., description="whether or not to sort conversations in ascending order")] = False,
        client_id: Annotated[
            Optional[str],
            Query(...,
                  description="Filter conversations based on Client ID in metadata")] = None,
        page_offset: Annotated[
            Optional[str],
            Query(...,
                  description="Page offset to get the next set of conversations. This is the str value of the last page_offset retrieved.",
                  example='{"latest_entry_date": 1723734909,"user_id": "XXXX","conversation_id_conversation_entry_id": "METADATA#XXXX"}')] = None
):
    log_version_info()
    logger.info(f"Getting conversation list for {user_id}, "
                f"from view '{x_tr_product_view}' in product '{x_tr_product_name}'")
    UserSession(request)
    filters = dict()
    not_equal_filters = dict()
    if client_id is not None:
        filters[Constants.CONV_META_CUSTOM] = {"client_id": client_id}
    if is_favorite is not None:
        if is_favorite:
            filters[Constants.CONV_META_IS_FAVORITE] = True
        else:
            not_equal_filters[Constants.CONV_META_IS_FAVORITE] = True  # Ensures backward compatibility
    response = dynamo_db_v2.get_conversation_list(user_id=user_id, page_limit=limit, page_offset=page_offset,
                                                  filters=filters, not_equal_filters=not_equal_filters,
                                                  ascending_order=ascending_order)
    set_span_http_code(status.HTTP_200_OK)
    return response



@conversation_router_v2.get(
    "/{user_id}/{conversation_id}",
    status_code=status.HTTP_200_OK,
    response_model=ConversationV2,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    responses=GET_CONVERSATION_V2_RESPONSES,
    tags=["Conversation Actions"]
)
async def get_conversation(
        user_id: str,
        conversation_id: str,
        request: Request,
        include_intermediate_results: bool = False,
):
    log_version_info()
    return get_conversation_v2(user_id=user_id, conversation_id=conversation_id,
                               include_intermediate_results=include_intermediate_results, request=request)


@conversation_router_v2.get(
    "/{user_id}/{conversation_id}/{conversation_entry_id}",
    status_code=status.HTTP_200_OK,
    response_model=ConversationEntryV2,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Conversation Actions"]
)
async def get_conversation_entry(user_id: str,
                                 conversation_id: str,
                                 conversation_entry_id: str,
                                 request: Request, ):
    try:
        log_version_info()
        user_session = UserSession(request)
        add_trace_context_to_current_span(conversation_id, conversation_entry_id, user_session.get_session_info())
        task_metadata = get_task_metadata(conversation_entry_id)
        conversation_entry = dynamo_db_v2.get_conversation_entry(
            user_id=user_id, conversation_id=conversation_id, conversation_entry_id=conversation_entry_id,
            include_rag_pipeline_output=False
        )

        profile_name = None
        if hasattr(conversation_entry, 'profile') and conversation_entry.profile:
            profile_name = conversation_entry.profile
            add_to_current_span("profile", profile_name)

        conversation_entry.task_metadata = task_metadata
        conversation_entry.percent_complete = task_metadata.get("percent_complete", None)
        # For V2, user_interaction_required should not be output
        if conversation_entry.user_interaction_required is not None:
            conversation_entry.user_interaction_required = None
        append_disclaimer(conversation_entry=conversation_entry, profile_name=profile_name)
        set_span_http_code(status.HTTP_200_OK)
        return conversation_entry

    except HTTPException as http_exception:
        raise http_exception
    except DynamoDBNotFoundException as dynamo_error:
        logger.error(dynamo_error)
        raise_http_error(status.HTTP_404_NOT_FOUND, detail=f"No conversation details found for user_id : {user_id} "
                                                           f", conversation_id : {conversation_id} and "
                                                           f"conversation_entry_id :{conversation_entry_id}.")
    except Exception as e:
        logger.error(e)
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e.__str__())


@conversation_router_v2.put(
    "/{user_id}/{conversation_id}/metadata",
    status_code=status.HTTP_200_OK,
    response_model=ConversationMetadataV2,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Metadata"]
)
async def update_conversation_metadata(
        conversation_metadata: Annotated[
            ConversationMetadataV2,
            Body(..., openapi_examples=UPDATE_CONVERSATION_METADATA_EXAMPLES),
        ],
        user_id: str,
        conversation_id: str,
        request: Request,
        validated_request: ConversationMetadataV2 = Depends(validate_custom_field_in_metadata)
):
    prevent_metadata_update(conversation_metadata=validated_request)
    user_session = UserSession(request)
    add_trace_context_to_current_span(conversation_id, "", user_session.get_session_info())
    attribute_updates, custom_metadata = process_conversation_metadata(validated_request)
    conversation = dynamo_db_v2.get_conversation(user_id=user_id, conversation_id=conversation_id,
                                                 include_results=False)
    if conversation:
        update_response = dynamo_db_v2.update_conversation(
            user_id=user_id, conversation_id=conversation_id,
            attribute_updates=attribute_updates,
            custom_metadata_updates=custom_metadata)
        if not update_response:
            raise UpdateConversationException()
        else:
            set_span_http_code(status.HTTP_200_OK)
            return dynamo_db_v2.get_conversation(user_id=user_id, conversation_id=conversation_id,
                                                 include_results=False).conversation_metadata
    else:
        raise ConversationNotFoundException()


@conversation_router_v2.put(
    "/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
    status_code=status.HTTP_200_OK,
    response_model=ConversationEntryMetadataV2,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Metadata"]
)
async def update_conversation_entry_metadata(
        conversation_entry_metadata: Annotated[
            ConversationEntryMetadataV2,
            Body(..., openapi_examples=UPDATE_CONVERSATION_ENTRY_METADATA_EXAMPLES),
        ],
        user_id: str,
        conversation_id: str,
        conversation_entry_id: str,
        request: Request,
        validated_request: ConversationEntryMetadataV2 = Depends(validate_custom_field_in_entry_metadata),
):
    prevent_metadata_update(conversation_entry_metadata=validated_request)
    user_session = UserSession(request)
    add_trace_context_to_current_span(conversation_id, conversation_entry_id, user_session.get_session_info())
    custom_metadata: ConversationEntryCustomMetadata = validated_request.custom
    email_notification: EmailNotification = validated_request.email_notification
    delattr(validated_request, "custom")
    delattr(validated_request, "email_notification")
    attribute_updates = validated_request.model_dump(exclude_none=True, by_alias=True)
    if email_notification:
        attribute_updates.update(email_notification.model_dump(exclude_none=True, by_alias=True))
    update_response = dynamo_db_v2.update_conversation_entry(
        user_id=user_id, conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        attribute_updates=attribute_updates,
        custom_metadata_updates=custom_metadata)
    if not update_response:
        raise UpdateConversationException()
    else:
        conversation_entry = dynamo_db_v2.get_conversation_entry(
            user_id=user_id, conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            include_rag_pipeline_output=False)
        set_span_http_code(status.HTTP_200_OK)
        return conversation_entry.conversation_entry_metadata.model_dump(
            exclude_none=True, by_alias=False)


@conversation_router_v2.post(
    "/{user_id}/entry",
    status_code=status.HTTP_200_OK,
    description=CREATE_CONVERSATION_DESCRIPTION,
    summary=CREATE_CONVERSATION_SUMMARY,
    response_model=StartConversationResponse,
    responses=CREATE_CONVERSATION_RESPONSES,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Start or Continue Conversation"]
)
async def start_conversation_entry_with_new_conversation_v2(
        start_conversation_request: Annotated[
            StartConversationRequest,
            Body(openapi_examples=CREATE_CONVERSATION_EXAMPLES),
        ],
        request: Request,
        user_id: Annotated[str, Path(title="user id", description=USER_ID_DESCRIPTION)],
        headers: Annotated[ConversationHeaders,Depends()],
        validated_conversation_request: StartConversationRequest = Depends(validate_conversation_request),
        auth_token: str = Depends(gcs_auth.authorize_with_rights),

):
    return start_or_continue_conversation_v2(request=request,
                                             user_id=user_id,
                                             start_conversation_request=validated_conversation_request,
                                             is_new_conversation=True,
                                             conversation_id=str(uuid.uuid4()),
                                             conversation_type=Constants.CONV_INITIAL_CONVERSATION_NAME,
                                             auth_token=auth_token)


@conversation_router_v2.put(
    "/{user_id}/{conversation_id}/entry",
    status_code=status.HTTP_200_OK,
    response_model=StartConversationResponse,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Start or Continue Conversation"]
)
async def start_conversation_entry_on_existing_conversation_v2(
        request: Request,
        user_id: str,
        conversation_id: str,
        start_conversation_request: StartConversationRequest = Depends(validate_conversation_request),
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        headers=Depends(ConversationHeaders)
):
    conversation = dynamo_db_v2.get_conversation(
        user_id=user_id,
        conversation_id=conversation_id,
        include_intermediate_results=False,
    )
    if conversation is None:
        raise ConversationNotFoundException()

    requested_profile: str = start_conversation_request.answer_solution_profile.lower()
    previous_profile: Optional[str] = get_original_profile(conversation.conversation_entries.copy())
    if not previous_profile:
        previous_profile = conversation.conversation_metadata.profile

    previous_profile_normalized: Optional[str] = previous_profile.lower()
    if previous_profile_normalized != requested_profile:
        logger.warn(f"Conversation profile mismatch, "
                    f"using original profile {previous_profile_normalized} instead of {requested_profile}")
        start_conversation_request.answer_solution_profile = previous_profile_normalized

    answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=start_conversation_request.answer_solution_profile)
    validate_conversation_for_followup(conversation, answer_profile)

    # Conversation history to chronological order
    conversation.conversation_entries.sort(key=lambda x: x.timestamp, reverse=True)

    return start_or_continue_conversation_v2(request=request,
                                             user_id=user_id,
                                             start_conversation_request=start_conversation_request,
                                             is_new_conversation=False,
                                             conversation_id=conversation_id,
                                             conversation_type=Constants.CONV_FOLLOWUP_CONVERSATION_NAME,
                                             auth_token=auth_token)


# ================= v1 Endpoints =================
@conversation_router.get(
    "/{user_id}/{conversation_id}",
    status_code=status.HTTP_200_OK,
    response_model=Conversation,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def get_conversation(
        user_id: str,
        conversation_id: str,
        include_intermediate_results: bool = False,
        request: Request = None
):
    with tracer.current_span() as span:
        try:
            conversation = dynamo_db.get_conversation(
                user_id=user_id,
                conversation_id=conversation_id,
                include_intermediate_results=include_intermediate_results,
            )
            if conversation is None:
                raise HTTPException(status_code=404, detail=f"No conversation details found for user_id :{user_id} and "
                                                            f"conversation_id : {conversation_id}.")
            else:
                conversation.conversation_history.sort(key=lambda x: x.timestamp, reverse=True)
                span.set_tag("http.status_code", "200")
                return conversation
        except HTTPException as http_exception:
            raise http_exception
        except Exception as e:
            span.set_tag("http.status_code", "500")
            logger.error(e)
            raise HTTPException(status_code=500, detail=e.__str__())


@conversation_router.get(
    "/{user_id}/{conversation_id}/{conversation_entry_id}",
    status_code=status.HTTP_200_OK,
    response_model=RetrieveConversationEntryResponse,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def retrieve_conversation_entry(user_id: str,
                                      conversation_id: str,
                                      conversation_entry_id: str,
                                      request: Request):
    result = get_task_info(conversation_entry_id)

    with tracer.current_span() as span:
        try:
            conversation_entry_or_status = dynamo_db.retrieve_conversation_entry(
                user_id=user_id, conversation_id=conversation_id, conversation_entry_id=conversation_entry_id
            )

            user_session = UserSession(request)
            add_trace_context_to_current_span(conversation_id, conversation_entry_id, user_session.get_session_info())

            span.set_tag("http.status_code", "200")
            if conversation_entry_or_status[0] == RetrieveConversationEntryStatuses.COMPLETE:
                r = RetrieveConversationEntryResponse(
                    user_id=user_id,
                    conversation_id=conversation_id,
                    conversation_entry_id=conversation_entry_id,
                    destination_url=conversation_entry_or_status[2].destination_url,
                    send_email=conversation_entry_or_status[2].send_email,
                    email_address=conversation_entry_or_status[2].email_address,
                    email_sent=conversation_entry_or_status[2].email_sent,
                    retrieve_conversation_entry_status=RetrieveConversationEntryStatuses.COMPLETE,
                    error_code=conversation_entry_or_status[3]["error_code"],
                    error_message=conversation_entry_or_status[3]["error_message"],
                    worker_task_status=result,
                    conversation_entry=conversation_entry_or_status[1],
                )
                return r
            else:
                return RetrieveConversationEntryResponse(
                    user_id=user_id,
                    conversation_id=conversation_id,
                    conversation_entry_id=conversation_entry_id,
                    destination_url=conversation_entry_or_status[2].destination_url,
                    send_email=conversation_entry_or_status[2].send_email,
                    email_address=conversation_entry_or_status[2].email_address,
                    email_sent=conversation_entry_or_status[2].email_sent,
                    retrieve_conversation_entry_status=conversation_entry_or_status[0],
                    error_code=conversation_entry_or_status[3]["error_code"],
                    error_message=conversation_entry_or_status[3]["error_message"],
                    worker_task_status=result,
                )
        except DynamoDBNotFoundException as dynamo_error:
            logger.error(dynamo_error)
            raise_http_error(status.HTTP_404_NOT_FOUND, detail=f"No conversation details found for user_id :{user_id}"
                                                               f", conversation_id : {conversation_id} and "
                                                               f"conversation_entry_id : {conversation_entry_id}.")
        except HTTPException as http_exception:
            raise http_exception
        except Exception as e:
            span.set_tag("http.status_code", "500")
            logger.error(e)
            raise HTTPException(status_code=500, detail=e.__str__())


@conversation_router.post(
    "/{user_id}/entry",
    status_code=status.HTTP_200_OK,
    response_model=StartAnswerGenerationResponse,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def start_conversation_entry_with_new_conversation(
        request: Request,
        user_id: str,
        start_answer_generation_request: StartAnswerGenerationRequest = Body(description="request body"),
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        x_tr_product: Optional[str] = Header(None, description="product where request originated from"),
        x_tr_product_addtl_key_name: Optional[str] = Header(
            None, description="name of product determined additional key"
        ),
        x_tr_product_addtl_key_value: Optional[str] = Header(
            None, description="value of product determined additional key"
        ),
        x_tr_email_addr: Optional[str] = Header(None, description="email address"),
        x_tr_send_email: Optional[bool] = Header(None, description="Send email if true"),
        x_tr_destination_url: Optional[str] = Header(None, description="https://foobar.com/path"),
):
    with tracer.current_span() as span:
        try:
            user_session = UserSession(request)
            user_classification = request.headers.get("x-tr-user-classification", "unknown").lower()
            add_to_current_span("user_classification", user_classification)

            start_answer_generation_response = create_conversation_entry(
                is_new_conversation=True,
                user_id=user_id,
                start_answer_generation_request=start_answer_generation_request,
                conversation_id=str(uuid.uuid4()),
                auth_token=auth_token,
                cobalt_session=user_session.get_session_info(),
                request=request,
                email_address=x_tr_email_addr,
                send_email=x_tr_send_email,
                destination_url=x_tr_destination_url,
                user_classification=user_classification,
                conversation_type=Constants.CONV_INITIAL_CONVERSATION_NAME,
            )

            dynamo_db.write_metadata(
                user_id=user_id,
                conversation_id=start_answer_generation_response.conversation_id,
                product=str(x_tr_product or ""),
                profile=start_answer_generation_request.answer_solution_profile,
                title=start_answer_generation_request.user_input,
                additional_key_name=str(x_tr_product_addtl_key_name or ""),
                additional_key_value=str(x_tr_product_addtl_key_value or ""),
            )

            add_to_current_span("profile", start_answer_generation_request.answer_solution_profile.lower())
            add_trace_context_to_current_span(start_answer_generation_response.conversation_id,
                                              start_answer_generation_response.conversation_entry_id,
                                              user_session.get_session_info())

            add_to_current_span("http.status_code", "200")

            return start_answer_generation_response
        except HTTPException as http_exception:
            raise http_exception
        except Exception as e:
            logger.error(e)
            raise HTTPException(status_code=500, detail=e.__str__())


@conversation_router.put(
    "/{user_id}/{conversation_id}/entry",
    status_code=status.HTTP_200_OK,
    response_model=StartAnswerGenerationResponse,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def start_conversation_entry_on_existing_conversation(
        user_id: str,
        conversation_id: str,
        start_answer_generation_request: StartAnswerGenerationRequest,
        request: Request,
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        x_tr_email_addr: Optional[str] = Header(None, description="email address"),
        x_tr_send_email: Optional[bool] = Header(None, description="Send email if true"),
        x_tr_destination_url: Optional[str] = Header(None, description="https://foobar.com/path"),
):
    try:
        with tracer.current_span() as span:
            user_session = UserSession(request)
            user_classification = request.headers.get("x-tr-user-classification", "unknown").lower()
            add_to_current_span("user_classification", user_classification)
            start_answer_generation_response = create_conversation_entry(
                is_new_conversation=False,
                user_id=user_id,
                start_answer_generation_request=start_answer_generation_request,
                conversation_id=conversation_id,
                auth_token=auth_token,
                cobalt_session=user_session.get_session_info(),
                request=request,
                email_address=x_tr_email_addr,
                send_email=x_tr_send_email,
                destination_url=x_tr_destination_url,
                user_classification=user_classification,
                conversation_type=Constants.CONV_FOLLOWUP_CONVERSATION_NAME,
            )

            timestamp = int(time.time())
            dynamo_db.update_metadata(
                user_id=user_id,
                conversation_id=conversation_id,
                attribute_updates={Constants.CONV_META_LATEST_ENTRY_DATE: timestamp},
            )

            add_to_current_span("profile", start_answer_generation_request.answer_solution_profile.lower())

            add_trace_context_to_current_span(start_answer_generation_response.conversation_id,
                                              start_answer_generation_response.conversation_entry_id,
                                              user_session.get_session_info())

            add_to_current_span("http.status_code", "200")
            return start_answer_generation_response
    except HTTPException as http_exception:
        raise http_exception
    except Exception as e:
        span.set_tag("http.status_code", "500")
        logger.error(e)
        raise HTTPException(status_code=500, detail=e.__str__())


@conversation_router.put(
    "/{user_id}/{conversation_id}/metadata",
    status_code=status.HTTP_200_OK,
    response_model=UpdateConversationMetadata,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    response_model_exclude_none=True,
    deprecated=True, tags=["Deprecated"],
)
async def update_conversation_metadata(
        user_id: str, conversation_id: str, update_conversation_metadata_request: UpdateConversationMetadata
):
    attribute_updates = {}
    if update_conversation_metadata_request.title:
        attribute_updates[Constants.CONV_META_TITLE] = update_conversation_metadata_request.title
    if update_conversation_metadata_request.additional_key_name:
        attribute_updates[Constants.CONV_META_ADDTL_KEY_NAME] = update_conversation_metadata_request.additional_key_name
    if update_conversation_metadata_request.additional_key_value:
        attribute_updates[
            Constants.CONV_META_ADDTL_KEY_VALUE
        ] = update_conversation_metadata_request.additional_key_value
    if attribute_updates:
        update_response = dynamo_db.update_metadata(
            user_id=user_id, conversation_id=conversation_id, attribute_updates=attribute_updates
        )
        if not update_response:
            raise HTTPException(
                status_code=404,
                detail=f"Conversation with id '{conversation_id}' was not found. " f"No metadata update occurred.",
            )
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Request did not contain any valid attributes "
                   f"{list(UpdateConversationMetadata.__fields__.keys())}",
        )
    return update_conversation_metadata_request


@conversation_router.put(
    "/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
    status_code=status.HTTP_200_OK,
    response_model=UpdateConversationEntryMetadata,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    response_model_exclude_none=True,
    deprecated=True, tags=["Deprecated"],
)
async def update_conversation_entry_metadata(
        user_id: str,
        conversation_id: str,
        conversation_entry_id: str,
        update_conversation_entry_metadata_request: UpdateConversationEntryMetadata,
):
    attribute_updates = {}
    if update_conversation_entry_metadata_request.email_address:
        attribute_updates[Constants.CONV_EMAIL_ADDR] = update_conversation_entry_metadata_request.email_address
    if update_conversation_entry_metadata_request.send_email is not None:
        attribute_updates[Constants.CONV_SEND_EMAIL] = update_conversation_entry_metadata_request.send_email
    if update_conversation_entry_metadata_request.destination_url:
        attribute_updates[Constants.CONV_DESTINATION_URL] = update_conversation_entry_metadata_request.destination_url
    if update_conversation_entry_metadata_request.result_viewed is not None:
        attribute_updates[Constants.CONV_RESULT_VIEWED] = update_conversation_entry_metadata_request.result_viewed
    if attribute_updates:
        if attribute_updates.get(Constants.CONV_RESULT_VIEWED):
            result_viewed_ts = None
            result_viewed_ts_item = dynamo_db.retrieve_item(
                partition_key=user_id, sort_key=f"{conversation_id}#{conversation_entry_id}"
            )
            if result_viewed_ts_item.get(Constants.CONV_RESULT_VIEWED_TIMESTAMP):
                result_viewed_ts = result_viewed_ts_item.get(Constants.CONV_RESULT_VIEWED_TIMESTAMP).get("N")
            if not result_viewed_ts:
                attribute_updates[Constants.CONV_RESULT_VIEWED_TIMESTAMP] = int(time.time())
        update_response = dynamo_db.update_entry_metadata(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_entry_id=conversation_entry_id,
            attribute_updates=attribute_updates,
        )
        if not update_response:
            raise HTTPException(
                status_code=404,
                detail=f"Conversation '{conversation_id}' "
                       f"entry '{conversation_entry_id}' was not found."
                       f"No metadata update occurred.",
            )
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Request did not contain any valid attributes "
                   f"{list(UpdateConversationMetadata.__fields__.keys())}",
        )
    return update_conversation_entry_metadata_request


@conversation_router.get(
    "-list/{user_id}",
    response_model=ConversationListMetadata,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def get_conversations(
        user_id: str,
        x_tr_page_offset: Optional[str] = Header(None, description="Starting point for result pagination"),
        x_tr_page_limit: Optional[int] = Header(
            100,
            description="Number of conversation metadata records per page. "
                        "Filtered results will count towards limit since "
                        "DynamoDB filters out results after limiting results",
        ),
        x_tr_product: Optional[str] = Header(None, description="Product filter"),
        x_tr_product_addtl_key_name: Optional[str] = Header(None, description="Additional Key Name filter"),
        x_tr_product_addtl_key_value: Optional[str] = Header(None, description="Additional Key Value filter"),
):
    try:
        filters = {
            Constants.CONV_META_PRODUCT: x_tr_product,
            Constants.CONV_META_ADDTL_KEY_NAME: x_tr_product_addtl_key_name,
            Constants.CONV_META_ADDTL_KEY_VALUE: x_tr_product_addtl_key_value,
        }
        return dynamo_db.get_conversations_metadata_by_user(
            user_id=user_id, page_offset=x_tr_page_offset, page_limit=x_tr_page_limit, filters=filters
        )
    except DynamoDBNotFoundException:
        raise_http_error(status.HTTP_404_NOT_FOUND, f"No conversation details found for user_id : '{user_id}.")
    except Exception as e:
        logger.error(e)
        raise_http_error(code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e.__str__())


@conversation_api_v1.post(
    "/summarize/{user_id}/keycite",
    response_model=StartAnswerGenerationResponse,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    deprecated=True, tags=["Deprecated"],
)
async def summarize_conversation_key_cite(
        user_id: str,
        summarize_answer_generation_request: SummarizeAnswerGenerationRequest,
        request: Request,
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
        x_tr_product: Optional[str] = Header(None, description="product where request originated from",
                                             ),
        x_tr_product_addtl_key_name: Optional[str] = Header(
            None, description="name of product determined additional key",
        ),
        x_tr_product_addtl_key_value: Optional[str] = Header(
            None, description="value of product determined additional key"
        ),
):
    return StartAnswerGenerationResponse(user_id=user_id, conversation_id=str(uuid.uuid4()))
