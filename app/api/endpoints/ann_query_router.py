import traceback
from urllib.parse import urlparse, quote

from fastapi import APIRouter
from fastapi import Depends, HTTPException
from raslogger import LoggingFactory

from api.auth import gcs_auth
from api.models.ann_query_models import AnnQueryInput
from api.models.sagemaker_models import SagemakerOutput

from app.utils.open_search import execute_similarity_query
from config import settings

logger = LoggingFactory.get_logger(__name__)
ann_query_router = APIRouter(prefix="/api/v1", tags=["V1"])
config_settings = settings.get_settings()


@ann_query_router.post(path="/ann_query",
                       dependencies=[Depends(gcs_auth.authorize_with_rights)],
                       tags=["Developer Endpoints"])
async def ann_query(ann_query_input: AnnQueryInput):
    try:
        # alias_name = 'cases_synopsis_paras_v2'
        if not is_safe_url(config_settings.OPEN_SEARCH_ENDPOINT):
            raise HTTPException(status_code=400, detail="Invalid URL")
        vector_field = "embedding"
        content_alias = quote(ann_query_input.content_alias)
        output = execute_similarity_query(
            ann_query_input.input_query,
            config_settings.OPEN_SEARCH_ENDPOINT,
            content_alias,
            vector_field,
            config_settings.OPEN_SEARCH_SECRET_ID,
            ann_query_input.size,
            ann_query_input.k,
            ann_query_input.jurisdictions_override,
            ann_query_input.included_fields,
            ann_query_input.excluded_fields,
        )

        hits = output["hits"]["hits"]
        return SagemakerOutput(candidates=hits)

    except Exception as e:
        # Capture and format the stack trace
        tb = traceback.format_exc()
        # Append the stack trace to the error message
        error_msg = f"{str(e)}\n\n{tb}"
        raise HTTPException(status_code=400, detail=error_msg)


def is_safe_url(url):
    url = urlparse(url)
    if not url.netloc:
        return False
    return True
