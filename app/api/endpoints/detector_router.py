import uuid
from typing import List, Annotated

from configuration_utils.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.enums import ConversationActionType, DataRetention
from conversation_core.shared.models.v2.conversation import ConversationMetadata as ConversationMetadataV2, \
    StartConversationResponse
from conversation_core.shared.models.v2.conversation_entry import \
    ConversationEntryMetadata as ConversationEntryMetadataV2
from conversation_core.shared.utils.trace import add_to_current_span, add_trace_context_to_current_span
from fastapi import APIRouter, Depends, Request, Body, Path
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from api.constants.doc import QCBS_REQUEST_EXAMPLE, CREATE_CONVERSATION_RESPONSES_V3
from api.models.conversations.user_session_model import UserSession
from api.models.detector_models import QCBSDetectorServiceInput, DetectorRequest
from app.api.exceptions.conversation_exceptions import SessionInfoException
from app.config.settings import Settings
from config import settings
from services.conversation_creation_service import generate_conversation_event
from tasks.shared.detector_tasks import send_detector_task
from utils.span_utils import set_span_http_code, add_user_classification_to_span
from utils.validation_utils import validate_qcbs_detector

logger = LoggingFactory.get_logger(__name__)
detector_router = APIRouter(prefix="/api/v1/detector", tags=["V1"])  # Detector Routers
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]


# New detector entry
def create_detector_conversation_entry(
        request: Request,
        auth_token: str,
        cobalt_session: dict,
        profile: str,
        user_id: str,
        brief_id: str,
        quotation_guids: List[str],
        brief_input: QCBSDetectorServiceInput,
        product: str,
        user_classification: str,
        data_retention: DataRetention,
        conversation_metadata: ConversationMetadataV2,
        conversation_entry_metadata: ConversationEntryMetadataV2,
) -> StartConversationResponse:
    add_to_current_span("conversation_type", "qcbs_detector")
    auth_token = auth_token.replace("Bearer ", "")
    conversation_id = str(uuid.uuid4())
    conversation_entry_id = str(uuid.uuid4())
    start_conversation_response = StartConversationResponse(user_id=user_id, conversation_id=conversation_id,
                                                            conversation_entry_id=conversation_entry_id)
    add_trace_context_to_current_span(conversation_id, start_conversation_response.conversation_entry_id, cobalt_session)

    dynamo_db_v2.write_detector(
        user_id=user_id,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        product=product,
        profile=profile,
        brief_id=brief_id,
        quotation_guids=quotation_guids,
        data_retention=data_retention.value,
        user_classification=user_classification,
        custom_conversation_metadata=conversation_metadata.custom,
        custom_conversation_entry_metadata=conversation_entry_metadata.custom,
    )

    dynamo_db_v2.store_brief_input(
        user_id=user_id,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        brief_input=brief_input.json().encode("utf-8"),
        data_retention=data_retention,
    )

    generate_conversation_event(
        auth_token=auth_token,
        cobalt_session=cobalt_session,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        request=request,
        profile=profile,
        action_type=ConversationActionType.DETECTOR,
    )

    route_to_green = True if request.headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False

    send_detector_task(
        user_id=user_id,
        brief_id=brief_id,
        quotation_guids=quotation_guids,
        answer_solution_profile=profile,
        conversation_id=conversation_id,
        conversation_entry_id=conversation_entry_id,
        conversation_action_type=ConversationActionType.DETECTOR,
        auth_token=auth_token,
        cobalt_session=cobalt_session,
        route_to_green=route_to_green,
        user_classification=user_classification,
    )

    start_conversation_response.conversation_metadata = (
        dynamo_db_v2.get_conversation(user_id=user_id,
                                      conversation_id=start_conversation_response.conversation_id,
                                      include_results=False,
                                      consistent_read=True)
        .conversation_metadata)
    start_conversation_response.conversation_entry_metadata = (
        dynamo_db_v2.get_conversation_entry(user_id=user_id,
                                            conversation_id=start_conversation_response.conversation_id,
                                            conversation_entry_id=start_conversation_response.conversation_entry_id,
                                            consistent_read=True)
        .conversation_entry_metadata)
    return start_conversation_response


@detector_router.post(
    "/{user_id}/qcbs",
    status_code=status.HTTP_200_OK,
    response_model=StartConversationResponse,
    response_model_exclude_none=True,
    responses=CREATE_CONVERSATION_RESPONSES_V3,
    response_model_by_alias=False,
    tags=["Non-Chat Conversation Actions"]
)
async def detect_quote_bs(
        qcbs_detector_service_request: Annotated[
            DetectorRequest,
            Body(..., openapi_examples=QCBS_REQUEST_EXAMPLE)
        ],
        request: Request,
        user_id: Annotated[str, Path(
            description="User ID to detect.",
            example="default-user-id"
        )],
        validate_request: DetectorRequest = Depends(validate_qcbs_detector),
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    brief_id = validate_request.user_input.brief_id
    quotation_guids = [quotation.original_quote_id
                       for quotation in validate_request.user_input.brief.quotations]

    user_session = UserSession(request)
    user_session_info = user_session.get_session_info()

    if user_session_info is None or Constants.SESSION_USER_SENSITIVITY not in user_session_info:
        raise SessionInfoException("User session info is missing or user sensitivity is not found. "
                                   "'x-tr-user-sensitivity' header is required if valid session not used.")
    user_classification = add_user_classification_to_span(user_session_info)

    start_conversation_response: StartConversationResponse = create_detector_conversation_entry(
        request=request,
        auth_token=auth_token,
        cobalt_session=user_session_info,
        user_id=user_id,
        brief_id=brief_id,
        quotation_guids=quotation_guids,
        brief_input=validate_request.user_input,
        profile=validate_request.answer_solution_profile,
        product="qcbs_detector",
        conversation_metadata=ConversationMetadataV2(),
        conversation_entry_metadata=ConversationEntryMetadataV2(),
        user_classification=user_classification,
        data_retention=DataRetention.ONE_DAY,
    )

    add_trace_context_to_current_span(start_conversation_response.conversation_id,
                                      start_conversation_response.conversation_entry_id,
                                      user_session.get_session_info())

    set_span_http_code(status.HTTP_200_OK)
    return start_conversation_response
