import uuid
from typing import List

from conversation_core.cobalt.utils import add_to_span, add_trace_context
from conversation_core.shared.dynamo_helper import ConversationDB
from conversation_core.shared.constants import Constants
from fastapi import APIRouter, Depends, HTTPException, Body, Request
from raslogger import LoggingFactory
from starlette import status

from api.auth import gcs_auth
from api.constants.doc import GET_INTENT_ANNOTATIONS_RESPONSES
from api.endpoints.conversation_router import create_conversation_entry
from api.models.conversations.user_session_model import UserSession
from conversation_core.shared.models.v3.conversation_entry import GroupedIntentAnnotations, IntentAnnotation
from api.models.conversations.submit_answer_models import StartAnswerGenerationResponse, StartAnswerGenerationRequest

from app.config.settings import Settings
from config import settings

logger = LoggingFactory.get_logger(__name__)
intent_router = APIRouter(prefix="/api/v1/intent", tags=["V1"])
settings: Settings = settings.get_settings()
dynamo_db = ConversationDB(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)


def get_blank_annotations(annotation_names: List[str]) -> List[IntentAnnotation]:
    intent_annotations = []
    for annotation_name in annotation_names:
        intent_annotations.append(IntentAnnotation(
            type=annotation_name,
            value=None
        ))
    return intent_annotations


@intent_router.get(
    path="/annotations",
    status_code=status.HTTP_200_OK,
    response_model=GroupedIntentAnnotations,
    include_in_schema=settings.ENVIRONMENT != "prod",
    response_model_exclude_none=True,
    responses=GET_INTENT_ANNOTATIONS_RESPONSES,
    tags=["Developer Endpoints"]
)
def get_grouped_intent_annotations():
    return GroupedIntentAnnotations(
        query_annotations=get_blank_annotations(Constants.QUERY_ANNOTATION_TYPES),
        intent_category_annotations=get_blank_annotations(Constants.INTENT_CATEGORY_ANNOTATION_TYPES),
        out_of_scope_intent_category_annotations=get_blank_annotations(
            Constants.OUT_OF_SCOPE_INTENT_CATEGORY_ANNOTATION_TYPES),
        jurisdiction_annotations=get_blank_annotations(Constants.JURISDICTION_ANNOTATION_TYPES),
        other_annotations=None,
        user_input=None
    )


@intent_router.post(
    "/{user_id}/evaluate",
    status_code=status.HTTP_200_OK,
    response_model=StartAnswerGenerationResponse,
    dependencies=[Depends(gcs_auth.authorize_with_rights)],
    include_in_schema=settings.ENVIRONMENT == "local" or settings.ENVIRONMENT == "ci" or settings.ENVIRONMENT == "dev",
    tags=["Developer Endpoints"]
)
async def evaluate_intent(request: Request,
                          user_id: str,
                          start_answer_generation_request: StartAnswerGenerationRequest = Body(
                              description="request body"),
                          auth_token: str = Depends(gcs_auth.authorize_with_rights)):
    try:
        user_session = UserSession(request)

        start_answer_generation_response = create_conversation_entry(
            is_new_conversation=True,
            user_id=user_id,
            start_answer_generation_request=start_answer_generation_request,
            conversation_id=str(uuid.uuid4()),
            auth_token=auth_token,
            cobalt_session=user_session.get_session_info(),
            request=request,
            email_address="",
            send_email=False,
            destination_url="",
            run_intent_resolver=True,
            auto_submit_task=False,
            conversation_type="Intent",
        )

        add_to_span("profile", start_answer_generation_request.answer_solution_profile.lower())
        add_trace_context(start_answer_generation_response.conversation_id,
                          start_answer_generation_response.conversation_entry_id,
                          user_session.get_session_info())

        add_to_span("http.status_code", "200")

        return start_answer_generation_response
    except HTTPException as http_exception:
        raise http_exception
    except Exception as e:
        logger.error(e)
        raise HTTPException(status_code=500, detail=e.__str__())
