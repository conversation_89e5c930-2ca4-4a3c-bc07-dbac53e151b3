import uuid

from app.config import settings
from starlette import status
from app.api.auth import gcs_auth

from raslogger import LoggingFactory
from typing import Optional, Annotated, Any
from app.config.settings import Settings
from conversation_core.shared.utils.version_info import get_non_rag_version_info
from fastapi import APIRouter, Depends, Request, Header, HTTPException, Body, Path, Query
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException, ConversationDB as ConversationDBV2
from conversation_core.shared.models.v2.conversation import Conversation as ConversationV2
from conversation_core.shared.models.v3.conversation import ConversationV3

from app.api.models.conversations.header_model import ConversationHeaders
from app.api.constants.doc import USER_ID_DESCRIPTION, GET_SNAPSHOT_DESCRIPTION, GET_SNAPSHOT_SUMMARY, \
    GET_SNAPSHOT_RESPONSES, GET_CONVERSATION_V2_RESPONSES
from app.api.constants.doc import CREATE_SNAPSHOT_DESCRIPTION, CREATE_SNAPSHOT_SUMMARY, CREATE_SNAPSHOT_RESPONSES
from app.services.snapshot_service import create_conversation_snapshot, get_conversation_snapshot

logger = LoggingFactory.get_logger(__name__)
snapshot_router = APIRouter(prefix="/api/v2/conversation/snapshot", tags=["V2"])
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]

async def validate_no_body(request: Request):
    body = await request.body()
    if body and body != b'':
        raise HTTPException(
            status_code=400,
            detail="Request body is not allowed"
        )

@snapshot_router.post(
    "/{user_id}/{conversation_id}",
    status_code=status.HTTP_200_OK,
    dependencies=gcs_auth_rights,
    description=CREATE_SNAPSHOT_DESCRIPTION,
    summary=CREATE_SNAPSHOT_SUMMARY,
    responses=CREATE_SNAPSHOT_RESPONSES,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    tags=["Conversation Snapshot"]
)
async def create_conversation_snapshot_v1(
    request: Request,
    user_id: Annotated[str, Path(title="user id", description=USER_ID_DESCRIPTION)],
    conversation_id: str,
    headers: Annotated[ConversationHeaders, Depends()],
    auth_token: str = Depends(gcs_auth.authorize_with_rights),
    _: None = Depends(validate_no_body)
):
    return create_conversation_snapshot(request=request,
                                        user_id=user_id,
                                        conversation_id=conversation_id,
                                        auth_token=auth_token)


@snapshot_router.get(
    path="/{snapshot_id}",
    status_code=status.HTTP_200_OK,
    dependencies=gcs_auth_rights,
    description=GET_SNAPSHOT_DESCRIPTION,
    summary=GET_SNAPSHOT_SUMMARY,
    response_model=Any,
    response_model_exclude_none=True,
    response_model_by_alias=False,
    responses=GET_CONVERSATION_V2_RESPONSES,
    tags=["Conversation Snapshot"]
)
async def get_conversation_snapshot_v1(
    request: Request,
    snapshot_id: str,
    headers: Annotated[ConversationHeaders, Depends()],
    auth_token: str = Depends(gcs_auth.authorize_with_rights),
    _: None = Depends(validate_no_body)
):
    return get_conversation_snapshot(snapshot_id=snapshot_id)
