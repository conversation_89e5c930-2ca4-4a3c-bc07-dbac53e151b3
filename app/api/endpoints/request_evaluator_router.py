import asyncio
import json
import time
import uuid
from typing import Annotated
from conversation_core.shared.services.llm_profile_service import LLMProfileEnvironment

from celery.exceptions import NotRegistered
from conversation_core.shared.constants import Constants
from conversation_core.shared.dynamo_helper_v2 import ConversationDB as ConversationDBV2
from conversation_core.shared.enums import ConversationActionType, RetrieveConversationEntryStatuses
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.request_evaluator import RequestEvaluatorResponse
from conversation_core.shared.models.v3.conversation import StartConversationRequestV3, \
    StartCommonIntentResolverRequestV3
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from fastapi import APIRouter, Depends, Request, Body
from fastapi import HTTPException
from fastapi.concurrency import run_in_threadpool
from raslogger import LoggingFactory
from starlette import status
from starlette.datastructures import Headers

from api.auth import gcs_auth
from app.api.constants.doc import CREATE_CONVERSATION_DESCRIPTION, CREATE_CONVERSATION_SUMMARY, \
    REQUEST_EVALUATOR_INPUT_VALIDATION_RESPONSES, \
    REQUEST_EVALUATOR_SKILL_ROUTER_RESPONSES, REQUEST_EVALUATOR_REQUEST
from app.config.settings import Settings
from config import settings
from services.chat_profile_service import answer_profile_service
from tasks.shared.conversation_tasks import CommonIRTask
from utils.validation_utils import validate_conversation_request

logger = LoggingFactory.get_logger(__name__)
request_evaluator_router = APIRouter(prefix="/api/v1/common", tags=["V1", "Request Evaluation"])
request_evaluator_router_v2 = APIRouter(prefix="/api/v2/common", tags=["V2", "Request Evaluation"])
settings: Settings = settings.get_settings()
dynamo_db_v2 = ConversationDBV2(settings.DYNAMO_TABLE_NAME, settings.S3_BUCKET, settings.REGION)
gcs_auth_rights = [Depends(gcs_auth.authorize_with_rights)]
common_ir_task = CommonIRTask()
REQUEST_EVALUATOR_REQUEST_TIMEOUT_SECONDS = 60
BASE_REQUEST_EVALUATOR_PROFILE = "request_evaluator_profile"


@request_evaluator_router.post(
    path="/input-validation",
    status_code=status.HTTP_200_OK,
    description="Input validation",
    summary="Determine language and other intent annotations for a request",
    response_model=RequestEvaluatorResponse,
    responses=REQUEST_EVALUATOR_INPUT_VALIDATION_RESPONSES,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False
)
async def input_validation(
        request: Request,
        start_conversation_request: Annotated[StartConversationRequestV3, Depends(validate_conversation_request),
        Body(openapi_examples=REQUEST_EVALUATOR_REQUEST)],
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    return_val = await submit_request_evaluator_task(
        start_conversation_request=start_conversation_request,
        headers=request.headers,
        action=ConversationActionType.INPUT_VALIDATION,
        auth_token=auth_token)
    return RequestEvaluatorResponse(
        status=RetrieveConversationEntryStatuses.COMPLETE,
        answer_solution_profile=start_conversation_request.answer_solution_profile,
        conversation_action_type=ConversationActionType.INPUT_VALIDATION,
        system_output=return_val,
        timestamp=int(time.time())
    )


@request_evaluator_router.post(
    path="/skill-router",
    status_code=status.HTTP_200_OK,
    description=CREATE_CONVERSATION_DESCRIPTION,
    summary=CREATE_CONVERSATION_SUMMARY,
    response_model=RequestEvaluatorResponse,
    responses=REQUEST_EVALUATOR_SKILL_ROUTER_RESPONSES,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False
)
async def skill_router(
        request: Request,
        start_conversation_request: Annotated[StartConversationRequestV3, Depends(validate_conversation_request),
        Body(openapi_examples=REQUEST_EVALUATOR_REQUEST)],
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    return_val = await submit_request_evaluator_task(
        start_conversation_request=start_conversation_request,
        headers=request.headers,
        action=ConversationActionType.SKILL_ROUTER,
        auth_token=auth_token)
    return RequestEvaluatorResponse(
        status=RetrieveConversationEntryStatuses.COMPLETE,
        answer_solution_profile=start_conversation_request.answer_solution_profile,
        conversation_action_type=ConversationActionType.SKILL_ROUTER,
        system_output=return_val,
        timestamp=int(time.time())
    )


async def submit_request_evaluator_task(
        start_conversation_request: StartConversationRequestV3,
        headers: Headers,
        action: ConversationActionType,
        auth_token: str
):
    task_id = f"{uuid.uuid4()}-iv"
    # write event with task id and conversation id (add in metadata if not)
    pipeline_name = start_conversation_request.pipeline_name
    route_to_green = True if headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False
    if not start_conversation_request.answer_solution_profile:
        raise ProfileNotFoundException(f"Profile name cannot be None or empty.")
    try:
        logger.info(f"start {action.value} task with task_id=" + task_id)
        task = await asyncio.wait_for(
            run_in_threadpool(
                lambda: get_request_evaluator_task(
                    start_conversation_request=start_conversation_request,
                    conversation_history=[],
                    action=action,
                    auth_token=auth_token,
                    pipeline_name=pipeline_name,
                    route_to_green=route_to_green,
                    meta_data={},
                    task_id=task_id
                )
            ), timeout=REQUEST_EVALUATOR_REQUEST_TIMEOUT_SECONDS
        )
        logger.info(f"end {action.value} task with task_id=" + task_id)
        return json.loads(task)
    except asyncio.TimeoutError as e:
        logger.error(f"Request evaluator task timed out: {e}")
        raise TimeoutError(f"Request evaluator task timed out")


def get_request_evaluator_task(
        start_conversation_request: StartConversationRequestV3,
        conversation_history: list,
        action: ConversationActionType,
        auth_token: str,
        pipeline_name: str,
        route_to_green: bool,
        meta_data: dict,
        task_id: str
):
    request_evaluator_profile = BASE_REQUEST_EVALUATOR_PROFILE
    answer_solution_profile = answer_profile_service.get_profile(
        name=start_conversation_request.answer_solution_profile.lower())
    if "-" in start_conversation_request.answer_solution_profile:
        # Adding suffixes from original request profile to the request evaluator base profile
        request_evaluator_profile = (request_evaluator_profile + "-"
                                     + start_conversation_request.answer_solution_profile.split("-", 1)[1])
    if hasattr(answer_solution_profile, Constants.LLM_PROXY_ENVIRONMENT) and \
            answer_solution_profile.__getattribute__(Constants.LLM_PROXY_ENVIRONMENT) == LLMProfileEnvironment.PROD \
            and "-prod" not in request_evaluator_profile:
        # Request evaluator profile should be prod if the original request profile is prod
        logger.info("Adding prod suffix to Request Eval profile to match original request profile")
        request_evaluator_profile = request_evaluator_profile + "-prod"
    logger.info("Request Eval profile: " + request_evaluator_profile)
    task = common_ir_task.request_evaluator_task.apply_async(
        args=[
            start_conversation_request.user_input,
            conversation_history,
            action,
            request_evaluator_profile,
            pipeline_name,
            start_conversation_request.subscribed_skills,
            auth_token,
            meta_data,
        ],
        kwargs=None,
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "green": route_to_green,
                 "user_classification": meta_data.get("user_classification", "unknown")},
    )
    task_response = task.get()
    logger.info(f"task {task_id} completed.")
    return task_response


@request_evaluator_router_v2.post(
    path="/input-validation",
    status_code=status.HTTP_200_OK,
    description="Input validation",
    summary="Determine language and other intent annotations for a request",
    response_model=RequestEvaluatorResponse,
    responses=REQUEST_EVALUATOR_INPUT_VALIDATION_RESPONSES,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False
)
async def input_validation_v2(
        request: Request,
        start_common_ir_request: Annotated[StartCommonIntentResolverRequestV3, Depends(validate_conversation_request),
        Body(openapi_examples=REQUEST_EVALUATOR_REQUEST)],
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    return_val = await submit_request_evaluator_task_v2(
        start_common_ir_request=start_common_ir_request,
        headers=request.headers,
        action=ConversationActionType.INPUT_VALIDATION,
        auth_token=auth_token)
    return RequestEvaluatorResponse(
        status=RetrieveConversationEntryStatuses.COMPLETE,
        answer_solution_profile=start_common_ir_request.evaluator_answer_solution_profile,
        conversation_action_type=ConversationActionType.INPUT_VALIDATION,
        system_output=return_val,
        timestamp=int(time.time())
    )


@request_evaluator_router_v2.post(
    path="/skill-router",
    status_code=status.HTTP_200_OK,
    description=CREATE_CONVERSATION_DESCRIPTION,
    summary=CREATE_CONVERSATION_SUMMARY,
    response_model=RequestEvaluatorResponse,
    responses=REQUEST_EVALUATOR_SKILL_ROUTER_RESPONSES,
    dependencies=gcs_auth_rights,
    response_model_exclude_none=True,
    response_model_by_alias=False
)
async def skill_router_v2(
        request: Request,
        start_common_ir_request: Annotated[StartCommonIntentResolverRequestV3, Depends(validate_conversation_request),
        Body(openapi_examples=REQUEST_EVALUATOR_REQUEST)],
        auth_token: str = Depends(gcs_auth.authorize_with_rights),
):
    return_val = await submit_request_evaluator_task_v2(
        start_common_ir_request=start_common_ir_request,
        headers=request.headers,
        action=ConversationActionType.SKILL_ROUTER,
        auth_token=auth_token)
    return RequestEvaluatorResponse(
        status=RetrieveConversationEntryStatuses.COMPLETE,
        answer_solution_profile=start_common_ir_request.evaluator_answer_solution_profile,
        conversation_action_type=ConversationActionType.SKILL_ROUTER,
        system_output=return_val,
        timestamp=int(time.time())
    )


async def submit_request_evaluator_task_v2(
        start_common_ir_request: StartCommonIntentResolverRequestV3,
        headers: Headers,
        action: ConversationActionType,
        auth_token: str
):
    task_id = f"{uuid.uuid4()}-iv"
    # write event with task id and conversation id (add in metadata if not)
    pipeline_name = start_common_ir_request.pipeline_name
    meta_data: dict = {}
    route_to_green = True if headers.get("x-bluegreen-routing", default="blue").lower() == "green" else False
    if not start_common_ir_request.evaluator_answer_solution_profile:
        raise ProfileNotFoundException(f"Profile name cannot be None or empty.")
    if start_common_ir_request.requesting_service_answer_solution_profile:
        meta_data["requesting_service_answer_solution_profile"] = start_common_ir_request.requesting_service_answer_solution_profile
    try:
        logger.info(f"start {action.value} task with task_id=" + task_id)
        task = await asyncio.wait_for(
            run_in_threadpool(
                lambda: get_request_evaluator_task_v2(
                    start_common_ir_request=start_common_ir_request,
                    conversation_history=[],
                    action=action,
                    auth_token=auth_token,
                    pipeline_name=pipeline_name,
                    route_to_green=route_to_green,
                    meta_data=meta_data,
                    task_id=task_id
                )
            ), timeout=REQUEST_EVALUATOR_REQUEST_TIMEOUT_SECONDS
        )
        logger.info(f"end {action.value} task with task_id=" + task_id)
        return json.loads(task)
    except asyncio.TimeoutError as e:
        logger.error(f"Request evaluator task timed out: {e}")
        raise TimeoutError(f"Request evaluator task timed out")
    except NotRegistered as e:
        logger.error(
            f"Request evaluator task received Not Registered error from Celery. Check evaluator_answer_solution_profile: {e}")
        raise HTTPException(
            status_code=404,
            detail=f"Request evaluator task not registered. Evaluator Profile may be incorrect."
        )


def get_request_evaluator_task_v2(
        start_common_ir_request: StartCommonIntentResolverRequestV3,
        conversation_history: list,
        action: ConversationActionType,
        auth_token: str,
        pipeline_name: str,
        route_to_green: bool,
        meta_data: dict,
        task_id: str
):
    request_evaluator_answer_profile: AnswerProfile = answer_profile_service.get_profile(
        name=start_common_ir_request.evaluator_answer_solution_profile.lower())
    requesting_answer_solution_profile = answer_profile_service.get_profile(
        name=start_common_ir_request.requesting_service_answer_solution_profile.lower())

    if request_evaluator_answer_profile:
        request_evaluator_profile = request_evaluator_answer_profile.name
    else:
        raise ProfileNotFoundException(f"Profile {start_common_ir_request.answer_solution_profile} not found!")

    if (start_common_ir_request.requesting_service_answer_solution_profile
            and "-" in start_common_ir_request.requesting_service_answer_solution_profile):
        # Adding suffixes from original request profile to the request evaluator base profile
        request_evaluator_profile = (request_evaluator_profile + "-"
                                     + start_common_ir_request.requesting_service_answer_solution_profile.split("-", 1)[1])
    if requesting_answer_solution_profile and \
            hasattr(requesting_answer_solution_profile, Constants.LLM_PROXY_ENVIRONMENT) and \
            requesting_answer_solution_profile.__getattribute__(
                Constants.LLM_PROXY_ENVIRONMENT) == LLMProfileEnvironment.PROD \
            and "-prod" not in request_evaluator_profile:
        # Request evaluator profile should be prod if the original request profile is prod
        logger.info("Adding prod suffix to Request Eval profile to match original request profile")
        request_evaluator_profile = request_evaluator_profile + "-prod"
    logger.info("Request Eval profile: " + request_evaluator_profile)
    task = common_ir_task.request_evaluator_task.apply_async(
        args=[
            start_common_ir_request.user_input,
            conversation_history,
            action,
            request_evaluator_profile,
            pipeline_name,
            start_common_ir_request.subscribed_skills,
            auth_token,
            meta_data,
        ],
        kwargs=None,
        task_id=task_id,
        headers={"time_sent": time.time(),
                 "green": route_to_green,
                 "user_classification": meta_data.get("user_classification", "unknown")},
    )
    task_response = task.get()
    logger.info(f"task {task_id} completed.")
    return task_response
