from typing import Optional, List

from pydantic import BaseModel


class AnnQueryBaseModel(BaseModel):
    class Config:
        orm_mode = False


class AnnQueryInput(AnnQueryBaseModel):
    input_query: str
    content_alias: str
    k: int
    size: int
    jurisdictions_override: Optional[List[str]]
    included_fields: Optional[List[str]]
    excluded_fields: Optional[List[str]]


class AnnQueryOutput(BaseModel):
    input_query: str
    content_type: str
    query_results: str
