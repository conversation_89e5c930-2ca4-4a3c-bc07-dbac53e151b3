from typing import Optional, List, Union

from pydantic import BaseModel, Field, computed_field


class Passage(BaseModel):
    """."""

    id: str

    passage_text: str

    indexed_xpath: str

    doc_guid: Optional[str] = None

    doc_serial_number: Optional[str] = None

    masked_text: Optional[str] = None


class OpinionPassage(Passage):

    id: str

    passage_index: int

    short_title: str

    section_id: int

    case_title: Optional[str] = None


class FootnotePassage(Passage):
    """."""

    footnote_id: str

    short_title: str

    passage_index: int


class CaseDoc(BaseModel):
    """."""

    doc_guid: str

    legacy_id: Optional[str] = None

    opinion_passages: List[OpinionPassage] = Field(default_factory=list)

    footnote_passages: List[FootnotePassage] = Field(default_factory=list)

    @computed_field(return_type=Optional[str])
    @property
    def short_title(self):
        if len(self.opinion_passages) > 0:
            return self.opinion_passages[0].short_title
        elif len(self.footnote_passages) > 0:
            return self.footnote_passages[0].short_title
        return None

    @computed_field(return_type=Optional[str])
    @property
    def doc_title(self):
        if len(self.opinion_passages) > 0:
            return self.opinion_passages[0].case_title
        return None


class CitedCaseDoc(CaseDoc):
    original_quote_id: str


class QuoteItem(BaseModel):
    """."""

    id: str
    type: str
    content: str
    original_content: Optional[str] = None  # This key needs to exist in the input data


class Quotation(BaseModel):
    """Single quotation in the input data"""

    original_quote_id: str

    match_type: str

    citation_type: str

    original_pre_quote: str

    original_post_quote: str

    original_quote_location: str

    is_block_quote: bool

    is_footnote: bool

    contains_brackets: bool

    contains_ellipses: bool

    contains_pincite_error: bool

    in_issue_segment: Optional[bool]

    original_quote_items: List[QuoteItem]

    matched_quote_items: Optional[List[QuoteItem]]

    guid: Optional[str] = None  # This key needs to exist in the input data, it doesn't as of now.

    matched_pre_quote: Optional[str] = None  # This key needs to exist in the input data, it doesn't as of now.

    matched_post_quote: Optional[str] = None  # This key needs to exist in the input data, it doesn't as of now.

    matched_quote_items: Optional[List[QuoteItem]]

    matched_quote_x_path: Optional[str] = None  # This key needs to exist in the input data, it doesn't as of now.

    matched_quote_ending_x_path: Optional[str] = None

    section_containing_quote: Optional[str] = None

    def get_text(self) -> str:
        """Return the text of the quotation."""
        contents = [item.content for item in self.original_quote_items]
        return "".join(contents)

    def get_context(self) -> str:
        """Return the concatenated text of the pre-quote items."""
        return self.original_pre_quote + " " + self.get_text() + " " + self.original_post_quote


class UserBriefDocument(BaseModel):
    """."""

    # this only contains quotations data at present. Other fields can be added as needed
    quotations: List[Quotation]


class QCBSDetectorServiceInput(BaseModel):
    """."""

    brief_id: str

    brief: UserBriefDocument


class DetectorRequest(BaseModel):
    answer_solution_profile: str = Field(
        ...,
        description="The Answer Configuration you would like to use. Basically which labs solution.",
        example=""
    )
    user_input: QCBSDetectorServiceInput = Field(..., )


class DetectionOutput(BaseModel):
    """."""

    original_quote_id: str

    quotation_text: str

    brief_context: str

    cited_case: CaseDoc

    bs_summary: str


class QCBSDetectorServiceOutput(BaseModel):

    brief_id: str

    brief: UserBriefDocument

    bs_summaries: List[DetectionOutput]
