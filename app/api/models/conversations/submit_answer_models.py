import uuid
from typing import Optional, List

from conversation_core.shared.models.v2.conversation import ConversationMetadata as ConversationMetadataV2
from conversation_core.shared.models.v2.conversation_entry import \
    ConversationEntryMetadata as ConversationEntryMetadataV2
from pydantic import BaseModel, validator, Field


class StartAnswerGenerationRequest(BaseModel):
    pass
    user_input: str = Field(..., description="text that the user entered in", example="How many cows in texas?")
    answer_solution_profile: str = Field(
        ...,
        description="The Answer Configuration you would like " "to use. Basically which labs solution.",
        example="pl_labs_chat1,"
    )
    jurisdictions_override: Optional[List[str]] = Field(
        None, description="The Fermi Jurisdictions Associated with the query", example=["MN-CS", "MN-CS-ALL"],
        max_items=3
    )
    content_types_override: Optional[List[str]] = Field(
        None,
        description="Supported Content Types. THis "
                    "probably goes away once placed "
                    "into RAS Config and is nested "
                    "under Suported Answer "
                    "Configurations",
        example=["CASE", "STATUTE", "R<PERSON><PERSON><PERSON><PERSON><PERSON>", "KNOWHOW", "ANALYTICAL"],
    )

    @validator("answer_solution_profile", "content_types_override")
    def convert_enum_case(cls, v):
        return v.upper() if isinstance(v, str) else v


class StartAnswerGenerationResponse(BaseModel):
    user_id: str
    conversation_id: str
    conversation_entry_id: str = None

    @validator("conversation_entry_id", pre=True, always=True)
    def set_conversation_entry_id(cls, v):
        return v or str(uuid.uuid4())


class SummarizeAnswerGenerationRequest(BaseModel):
    answer_solution_profile: str = Field(
        ...,
        description="""The Answer Configuration you would like to use. Basically which labs solution.""",
        example="key_cite_profile1",
    )
    headnote_id: Optional[str] = Field(None, description="Headnote ID for the doc you want to summarize")
    legacy_id: Optional[str] = Field(None, description="Legacy ID of the Doc")
    citing_case_ids: Optional[List[str]] = Field(None, description="Cases citing this headnote")
    conversation_metadata: Optional[ConversationMetadataV2] = Field(None,
                                                                    description="Metadata for this summary conversation")
    conversation_entry_metadata: Optional[ConversationEntryMetadataV2] = Field(None,
                                                                               description="Entry metadata for this summary conversation")

    @validator("answer_solution_profile")
    def convert_enum_case(cls, v):
        return v.upper() if isinstance(v, str) else v
