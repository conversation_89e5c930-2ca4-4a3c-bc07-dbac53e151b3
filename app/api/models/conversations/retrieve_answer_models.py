from typing import Optional

from conversation_core.shared.enums import RetrieveConversationEntryStatuses
from conversation_core.shared.models.conversation import ConversationEntryResult
from pydantic import BaseModel, Field


class RetrieveConversationEntryRequest(BaseModel):
    user_id: str = Field(..., description="The user id. Could be cobalt user id")
    conversation_id: str
    conversation_entry_id: str


class RetrieveConversationEntryResponse(BaseModel):
    user_id: str
    conversation_id: str
    conversation_entry_id: str
    destination_url: str
    send_email: bool
    email_address: str
    email_sent: bool
    retrieve_conversation_entry_status: RetrieveConversationEntryStatuses = Field(
        ...,
        description='The current state of the submitted conversation entry. "Complete" indicates a successful response while "In Progress" indicates you need to check again later',
        example=RetrieveConversationEntryStatuses.COMPLETE,
    )
    error_code: Optional[int] = 0,
    error_message: Optional[str] = None,
    worker_task_status: Optional[dict]
    conversation_entry: Optional[ConversationEntryResult] = None
