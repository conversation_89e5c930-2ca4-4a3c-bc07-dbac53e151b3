from typing import Optional

from pydantic import BaseModel, Field


class UpdateConversationMetadata(BaseModel):
    title: Optional[str] = Field(None, description="updated title")
    additional_key_name: Optional[str] = Field(None, description="updated additional key name")
    additional_key_value: Optional[str] = Field(None, description="updated additional key value")


class UpdateConversationEntryMetadata(BaseModel):
    email_address: Optional[str] = Field(None, description="email address to send notification mail to")
    destination_url: Optional[str] = Field(None, description="url to be used for link to results")
    send_email: Optional[bool] = Field(None, description="whether or not to send results notification email")
    result_viewed: Optional[bool] = Field(None, description="used to track whether or not the user has viewed results")

    class Config:
        extra = "forbid"
