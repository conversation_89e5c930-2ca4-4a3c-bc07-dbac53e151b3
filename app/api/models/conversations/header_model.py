
from typing import Optional
from typing_extensions import Annotated

from fastapi import Depends, FastAPI, <PERSON><PERSON>, Request
from pydantic import BaseModel, Field


def as_header(cls):
    """decorator for pydantic model
    replaces the Signature of the parameters of the pydantic model with `Header`
    """
    args = []
    for arg in cls.__signature__.parameters.values():
        description = cls.model_fields[arg.name].description
        default = Header(..., description=description) if arg.default is arg.empty else Header(arg.default, description=description)
        res = arg.replace(default=default)
        args.append(res)
    cls.__signature__ = cls.__signature__.replace(
        parameters=args
    )
    return cls


@as_header
class ConversationHeaders(BaseModel):
    x_trmr_userguid: Optional[str] = Field(default=None, description=" ", serialization_alias="x-trmr-userguid")
    x_trmr_sessionid: Optional[str] = Field(default=None, description=" ", serialization_alias="x-trmr-sessionid")
    co_sessiontoken: Optional[str] = Field(default=None, description=" ", serialization_alias="co-sessiontoken")
    x_cobalt_security_sessionid: Optional[str] = Field(default=None, description="The cobalt search session id", serialization_alias="x-cobalt-security-sessionid")
    x_cobalt_security_userguid: Optional[str] = Field(default=None, description="The cobalt search userguid", serialization_alias="x-cobalt-security-userguid")
    x_cobalt_security_onepassusernameencoded: Optional[str] =  Field(default=None, description=" ", serialization_alias="x-cobalt-security-onepassusernameencoded")
    x_cobalt_security_uds: Optional[str] = Field(default=None, description="Cobalt search uds endpoint", serialization_alias="x-cobalt-security-uds")
    x_tr_sessionid: Optional[str] = Field(default=None, description=" ", serialization_alias="x-tr-sessionid")
    x_tr_userid: Optional[str] = Field(default=None, description=" ", serialization_alias="x-tr-userid")
    x_tr_user_classification: Optional[str] = Field(default=None, description=" ", serialization_alias="x-tr-user-classification")
    x_tr_user_sensitivity: Optional[str] = Field(default=None, description=" ", serialization_alias="x-tr-user-sensitivity")
    x_cobalt_security_logprofile: Optional[str] = Field(default=None, description=" ", serialization_alias="x-cobalt-security-logprofile")
    x_cobalt_security_productview: Optional[str] = Field(default=None, description=" ", serialization_alias="x-cobalt-security-productview")
    x_trmr_product: Optional[str] = Field(default=None, description=" ", serialization_alias="x-trmr-product")
    x_tr_product_view: Optional[str] = Field(default=None, description="The product view field", serialization_alias="x-tr-product-view")
    x_tr_product_name: Optional[str] = Field(default=None, description="The product name field", serialization_alias="x-tr-product-name")
