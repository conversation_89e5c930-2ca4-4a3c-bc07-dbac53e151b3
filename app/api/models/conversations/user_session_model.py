from common_rest_utils.session.user_session import UserSession as UserSessionBase, <PERSON>er<PERSON><PERSON>
from fastapi import Request, HTTPException


class UserSession(UserSessionBase):
    def __init__(self, request: Request):
        self.is_v1: bool = "v1" in str(request.url).lower()

        super().__init__(request)


    def _validate_required_headers(self):
        product_name = self._get_product_name()
        if not self.is_v1 and not product_name:
            msg = f"Product name ({HeaderKey.PRODUCT_NAME.value}) not found in headers. This is a required header."
            raise HTTPException(status_code=400, detail=msg)

        product_view = self._get_product_view()
        if not self.is_v1 and not product_view:
            msg = f"Product view ({HeaderKey.PRODUCT_VIEW.value}) not found in headers. This is a required header."
            raise HTTPException(status_code=400, detail=msg)
