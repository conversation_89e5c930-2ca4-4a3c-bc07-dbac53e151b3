from conversation_core.shared.constants import Constants
from conversation_core.shared.enums import AalpSkill


class ConversationNotFoundException(Exception):
    pass


class InvalidConversationOverrideException(Exception):
    def __init__(self, invalid_override: str):
        self.invalid_override = invalid_override
        super(InvalidConversationOverrideException, self).__init__()

    def __str__(self):
        return f"Invalid override {self.invalid_override}. Valid overrides are {Constants.VALID_OVERRIDES}"


class InvalidConversationFilterException(Exception):
    def __init__(self, invalid_filter: str):
        self.invalid_filter = invalid_filter
        super(InvalidConversationFilterException, self).__init__()

    def __str__(self):
        return f"Invalid filter {self.invalid_filter}. Valid filters are {Constants.VALID_FILTERS}"


class InvalidConversationSkillException(Exception):
    def __init__(self, subscribed_skills: str):
        self.subscribed_skills = subscribed_skills
        super(InvalidConversationSkillException, self).__init__()

    def __str__(self):
        valid_skills = ', '.join(skill.value for skill in AalpSkill)
        return f"Invalid skill(s) {self.subscribed_skills}. Valid skills are: {valid_skills}"


class DeleteConversationException(Exception):
    def __init__(self, status):
        self.status = status
        super(DeleteConversationException, self).__init__()


class UpdateConversationException(Exception):
    pass


class SessionInfoException(Exception):
    pass


class ConversationMaxHoursExceededException(Exception):
    def __init__(self, conversation_id: str, status_code: int = 434):
        self.conversation_id = conversation_id
        self.status_code = status_code
        super(ConversationMaxHoursExceededException, self).__init__()

    def __str__(self):
        return f"Conversation {self.conversation_id} has exceeded the max hours allowed since " \
               f"creation of conversation"


class ConversationMaxEntriesExceededException(Exception):
    def __init__(self, conversation_id: str, answer_profile_name: str, status_code: int = 432):
        self.conversation_id = conversation_id
        self.status_code = status_code
        self.answer_profile_name = answer_profile_name
        super(ConversationMaxEntriesExceededException, self).__init__()

    def __str__(self):
        return (f"Conversation {self.conversation_id} has exceeded the max entries allowed "
                f"for a conversation with profile {self.answer_profile_name}")


class ConversationInProgressEntryException(Exception):
    def __init__(self, status_code: int = 433):
        self.status_code = status_code
        super(ConversationInProgressEntryException, self).__init__()

    def __str__(self):
        return ("Conversation has an in-progress entry. "
                "Please wait until that entry has completed before submitting follow-ups")
