from fastapi import Request
from fastapi.responses import JSONResponse
from starlette import status
import traceback
from fastapi.encoders import jsonable_encoder
from fastapi.exception_handlers import (
    http_exception_handler
)
from fastapi.exceptions import RequestValidationError
from conversation_core.shared.utils.trace import add_to_current_span
from starlette.exceptions import HTTPException as StarletteHTTPException
from raslogger import LoggingFactory

logger = LoggingFactory.get_logger(__name__)


def set_span_http_code(code: int):
    add_to_current_span("http.status_code", str(code))


def write_to_log(exc: Exception):
    trace = "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))
    logger.error("Error description %s Traceback\n%s", str(exc), trace)


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    set_span_http_code(status.HTTP_400_BAD_REQUEST)
    write_to_log(exc)
    return J<PERSON><PERSON>esponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"detail": jsonable_encoder(exc.errors())},
    )


async def unexpected_exception_handler(req: Request, ex: Exception):
    set_span_http_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
    write_to_log(ex)
    return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        content={"detail": ex.__str__()})


async def app_http_exception_handler(request: Request, ex: StarletteHTTPException):
    set_span_http_code(ex.status_code)
    write_to_log(ex)
    return await http_exception_handler(request, ex)


def init_exception_handlers(app):
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(StarletteHTTPException, app_http_exception_handler)
    app.add_exception_handler(Exception, unexpected_exception_handler)
