import traceback
from typing import Union

from fastapi import Request

from fastapi.responses import J<PERSON><PERSON>esponse
from starlette import status
from app.api.exceptions.conversation_exceptions import (
    DeleteConversationException,
    ConversationNotFoundException,
    UpdateConversationException,
    InvalidConversationOverrideException,
    InvalidConversationFilterException,
    SessionInfoException, ConversationMaxHoursExceededException, ConversationMaxEntriesExceededException,
    ConversationInProgressEntryException, InvalidConversationSkillException
)
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from app.api.models.conversations.operation_status import OperationStatus
from botocore.exceptions import ClientError
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException
from raslogger import LoggingFactory
from conversation_core.shared.utils.trace import add_to_current_span

logger = LoggingFactory.get_logger(__name__)

CONVERSATION_NOT_FOUND_MSG = "Conversation with conversationId {} for userId {} was not found."
CONVERSATION_ENTRY_NOT_FOUND_MSG = (
    "No conversation details found for user_id : {user_id} "
    ", conversation_id : {conversation_id} and "
    "conversation_entry_id :{conversation_entry_id}."
)
DYNAMODB_NOT_NOT_FOUND_MSG = "No conversation details found for user_id : '{}'."
UPDATE_CONVERSATION_FAIL_MSG = "Unable to make update to conversation entry metadata"

# Conversation endpoint related consts
CONVERSATION_ENTRY_V2_MD_URL_LAST_PATH = "metadata"


def write_to_log(ex: Exception, with_trace: bool=True):
    if with_trace:
        trace = "".join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        logger.error("Error description %s Traceback\n%s", str(ex), trace)
    else:
        logger.error(str(ex))


def set_span_http_code(code: int):
    add_to_current_span("http.status_code", str(code))


async def delete_conversation_exception_handler(req: Request, ex: DeleteConversationException) -> JSONResponse:
    conversation_id = req.path_params.get("conversation_id")
    user_id = req.path_params.get("user_id")
    if ex.status == OperationStatus.DELETE_CONVERSATION_FAILURE:
        set_span_http_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
        write_to_log(ex)
        return JSONResponse(
            content={
                "detail": f"Failed to delete conversation(s) with conversationId {conversation_id} for userId {user_id}."
            },
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    elif ex.status == OperationStatus.DELETE_CONVERSATION_NOT_FOUND:
        set_span_http_code(status.HTTP_404_NOT_FOUND)
        write_to_log(ex)
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={
                "detail": CONVERSATION_NOT_FOUND_MSG.format(conversation_id, user_id)
            })


async def conversation_not_found_exception_handler(req: Request, ex: ConversationNotFoundException):
    conversation_id = req.path_params.get("conversation_id")
    user_id = req.path_params.get("user_id")
    set_span_http_code(status.HTTP_404_NOT_FOUND)
    write_to_log(ex)
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            "detail": CONVERSATION_NOT_FOUND_MSG.format(conversation_id, user_id)
        })


async def bad_request_exception_handler(
        request: Request,
        ex: Union[
            ValueError,
            ClientError,
            InvalidConversationOverrideException,
            InvalidConversationFilterException,
            SessionInfoException]):
    set_span_http_code(status.HTTP_400_BAD_REQUEST)
    write_to_log(ex)
    return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content={"detail": ex.__str__()})


async def dynamodb_not_found_exception_handler(req: Request, ex: DynamoDBNotFoundException):
    conversation_id = req.path_params.get("conversation_id")
    conversation_entry_id = req.path_params.get("conversation_entry_id")
    user_id = req.path_params.get("user_id")
    method = req.method
    msg = ""
    # Check if Exception comes from which conversation route
    if not conversation_entry_id and method == "PUT" and CONVERSATION_ENTRY_V2_MD_URL_LAST_PATH in req.url.path:
        # exception raised if conversation_entry not found
        msg = CONVERSATION_ENTRY_NOT_FOUND_MSG.format(
            user_id=user_id, conversation_id=conversation_id, conversation_entry_id=conversation_entry_id
        )
    else:
        # then conversation not found
        msg = CONVERSATION_NOT_FOUND_MSG.format(conversation_id, user_id)
    # send response
    set_span_http_code(status.HTTP_404_NOT_FOUND)
    write_to_log(ex)
    return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content={"detail": msg})


async def update_conversation_exception_handler(req: Request, ex: UpdateConversationException):
    set_span_http_code(status.HTTP_404_NOT_FOUND)
    write_to_log(ex)
    return JSONResponse(status_code=status.HTTP_304_NOT_MODIFIED, content={"detail": UPDATE_CONVERSATION_FAIL_MSG})


async def timeout_handler(req: Request, ex: TimeoutError):
    set_span_http_code(status.HTTP_408_REQUEST_TIMEOUT)
    write_to_log(ex)
    return JSONResponse(status_code=status.HTTP_408_REQUEST_TIMEOUT, content={"detail": str(ex)})


async def max_exceeded_handler(req: Request, ex: Union[ConversationMaxHoursExceededException,
                                     ConversationMaxEntriesExceededException,
ConversationInProgressEntryException]):
    set_span_http_code(ex.status_code)
    write_to_log(ex, with_trace=False)
    return JSONResponse(status_code=ex.status_code, content={"detail": str(ex)})


def init_exception_handlers(app):
    app.add_exception_handler(TimeoutError, timeout_handler)
    app.add_exception_handler(ConversationInProgressEntryException, max_exceeded_handler )
    app.add_exception_handler(ConversationMaxEntriesExceededException, max_exceeded_handler)
    app.add_exception_handler(ConversationMaxHoursExceededException, max_exceeded_handler)
    app.add_exception_handler(SessionInfoException, bad_request_exception_handler)
    app.add_exception_handler(ProfileNotFoundException, bad_request_exception_handler)
    app.add_exception_handler(InvalidConversationFilterException, bad_request_exception_handler)
    app.add_exception_handler(InvalidConversationOverrideException, bad_request_exception_handler)
    app.add_exception_handler(ClientError, bad_request_exception_handler)
    app.add_exception_handler(ConversationNotFoundException, conversation_not_found_exception_handler)
    app.add_exception_handler(DeleteConversationException, delete_conversation_exception_handler)
    app.add_exception_handler(DynamoDBNotFoundException, dynamodb_not_found_exception_handler)
    app.add_exception_handler(UpdateConversationException, update_conversation_exception_handler)
    app.add_exception_handler(ValueError, bad_request_exception_handler)
    app.add_exception_handler(InvalidConversationSkillException, bad_request_exception_handler)
