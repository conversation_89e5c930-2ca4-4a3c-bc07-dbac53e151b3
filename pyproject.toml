[tool.poetry]
name = "ras-search-ai-conversations"
version = "0.1.0"
description = "AI Conversation Endpoints"
authors = ["<PERSON> <john.engel<PERSON>@thomsonreuters.com>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "~3.11"
setuptools = "78.1.1"
fastapi = "^0.115.5"
gunicorn = "23.0.0"
uvicorn = {extras = ["standard"], version = "0.34.3"}
auth = "0.5.3"
python-dotenv = "1.0.1"
api = "0.0.7"
moto="5.0.11"
pandas = "1.5.3"
psutil = "5.9.5"
filelock = "^3.12.2"
dataclasses-json = "^0.5.14"
ras-shared-python-aws-utils="^1.17.27"
ras-shared-python-conversation-core="^1.17.27"
ras-shared-python-common-utils="^1.17.27"
ras-shared-python-configuration-utils="^1.17.27"
ras-shared-python-events-client="^1.17.27"
ras-shared-python-datadog-utils="^1.17.27"
ras-shared-python-gcs-utils="^1.17.27"
ras-shared-python-common-rest-utils="^1.17.27"
cryptography = "44.0.1"
httpcore = "^1.0.5"
urllib3 = "1.26.20"
ras-shared-python-ai-conversations-qa-testing = "^1.17.27"
regex = "2024.9.11"

[tool.black]
# See other configurations available at https://pypi.org/project/black/
line-length = 120
target-version = ['py311']

[tool.poetry.group.dev.dependencies]
black = "23.3.0"
mock = "^5.0.2"
mockito = "^1.4.0"
pytest="8.2"
pytest-env="1.0.0"
poethepoet = "^0.23.0"
pytest-asyncio = "^0.24.0"
pytest-cov = "^2.10.0"
pytest-html = "^3.2.0"
ras-shared-python-ai-conversations-qa-testing="^1.16.0"

[[tool.poetry.source]]
name = "tr"
url = "https://tr1.jfrog.io/artifactory/api/pypi/pypi/simple"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
pre-commit-checks = "pre-commit run --all-files"
test = ["pre-commit-checks", "pytest"]
pytest = "pytest --cache-clear --junitxml=reports/pytest_junit.xml --html=reports/pytest.html --self-contained-html --cov-report=html:reports/coverage_html --cov-report=xml:reports/coverage.xml"
