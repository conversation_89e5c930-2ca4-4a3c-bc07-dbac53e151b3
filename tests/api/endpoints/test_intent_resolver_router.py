from unittest.mock import patch, Mock

from fastapi import HTTPException

from app.api.models.conversations.submit_answer_models import StartAnswerGenerationResponse
import pytest


class TestIntentResolverRouter:
    @patch("app.tasks.shared.conversation_tasks.IntentResolverTask.evaluate_intent_task.apply_async")
    @patch("app.api.endpoints.intent_resolver_router.create_conversation_entry")
    def test_evaluate_intent(self, mock_conv_entry, mock_apply_async, client):
        user_id = 'user123'
        ans_gen_response = StartAnswerGenerationResponse(user_id=user_id, conversation_id="15698")
        mock_conv_entry.return_value = ans_gen_response
        mock_apply_async.task_id.return_value = 'fake_id'

        payload = {
            "user_input": "How many cows in texas?",
            "answer_solution_profile": "practical_chat_profile1",
            'jurisdictions_override': ["MN-CS", "MN-CS-ALL"],
        }
        response = client.post(f"/api/v1/intent/{user_id}/evaluate", json=payload)
        assert response.status_code == 200
        response_json = response.json()
        assert response_json.get('user_id') == user_id
        assert response_json.get('conversation_id') is not None
        assert response_json.get('conversation_entry_id') is not None

    @patch("app.tasks.shared.conversation_tasks.IntentResolverTask.evaluate_intent_task.apply_async")
    @patch("app.api.endpoints.intent_resolver_router.create_conversation_entry")
    def test_evaluate_intent_exception(self, mock_conv_entry, mock_apply_async, client):
        user_id = 'user1231'
        mock_conv_entry.return_value = Exception("error")
        mock_apply_async.return_value = None
        payload = {
            "user_input": "How many cows in texas??",
            "answer_solution_profile": "practical_chat_profile1",
            'jurisdictions_override': ["MN-CS", "MN-CS-ALL"],
        }
        response = client.post(f"/api/v1/intent/{user_id}/evaluate", json=payload)
        assert response.status_code == 500

    @patch("app.tasks.shared.conversation_tasks.IntentResolverTask.evaluate_intent_task.apply_async")
    @patch("app.api.endpoints.intent_resolver_router.create_conversation_entry")
    def test_evaluate_intent_http_exception(self, mock_conv_entry, mock_apply_async, client):
        user_id = 'user1231'
        mock_conv_entry.return_value = Exception("error")
        mock_apply_async.task_id.return_value = 'fake_id'
        payload = {
            "user_input": "How many cows in texas??",
            # "answer_solution_profile": "practical_chat_profile1",
            'jurisdictions_override': ["MN-CS", "MN-CS-ALL"]
        }
        mock_conv_entry.side_effect = HTTPException(status_code=400, detail="Not Found")
        response = client.post(f"/api/v1/intent/{user_id}/evaluate", json=payload)
        assert response.status_code == 400
