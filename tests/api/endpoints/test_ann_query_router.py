from unittest.mock import patch


class TestAnnQueryRouter:
    @patch("app.api.endpoints.ann_query_router.execute_similarity_query")
    def test_ann_query(self, mock_execute_similarity_query, mock_answer_profile_service_instance, client):
        mock_execute_similarity_query.return_value = {'hits': {'hits': [{
            "passage_text": "execute",
            "opensearch_url": "https://example.com",
            "alias_name": "as",
            "vector_field": "vector",
            "open_search_secret_id": "145689",
            "size": 4,
            "k": 4,
            "jurisdiction_override": ["JUR_1", "JUR_2"],
            "included_fields": ["INC_1", "INC_2"],
            "excluded_fields": ["EXC_1", "EXC_2"]
        }]
        }
        }

        payload = {
            "input_query": "open carry",
            "content_alias": "cases_synopsis_paras_v1",
            "size": 4,
            "k": 25,
            "jurisdictions_override": [],
            "included_fields": ["passage_text", "doc_guid", "fermi_juris"],
            "excluded_fields": ["fermi_juris"]
        }

        response = client.post(f"/api/v1/ann_query", json=payload)
        assert response.status_code == 200

    @patch('conversation_core.shared.services.profile_service.AnswerProfileService', autospec=True)
    def test_ann_query_exception(self, mock_answer_profile_service_instance, client):
        payload = {
            "input_query": "open carry",
            "content_alias": "588",
            "size": 4,
            "k": 4,
            "jurisdictions_override": [],
            "included_fields": ["passage_text", "doc_guid", "fermi_juris"],
            "excluded_fields": ["fermi_juris"]
        }
        response = client.post(f"/api/v1/ann_query", json=payload)
        assert response.status_code == 400
