import time
import unittest
from unittest.mock import patch, MagicMock

import pytest
from configuration_utils.constants import Constants
from conversation_core.shared.constants import Constants as ConversationConstants
from conversation_core.shared.dynamo_helper import ConversationEntryResult, ConversationEntryEmailData
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException
from conversation_core.shared.enums import ConversationActionType, RetrieveConversationEntryStatuses, DataRetention
from conversation_core.shared.enums import IntentClassification
from conversation_core.shared.models.answer_profile import AnswerProfile
from conversation_core.shared.models.v2.conversation import Conversation, ConversationEntry, ConversationEntryMetadata, \
    ConversationMetadata, StartConversationResponse
from conversation_core.shared.models.v2.conversation import StartConversationRequest
from conversation_core.shared.models.v2.conversation_entry import EmailNotification
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from fastapi import HTTPException, status, Request
from starlette.datastructures import Headers
from botocore.exceptions import C<PERSON><PERSON>rror

from app.api.models.conversations.submit_answer_models import StartAnswerGenerationResponse, \
    StartAnswerGenerationRequest, SummarizeAnswerGenerationRequest
from app.api.models.conversations.update_metadata_models import UpdateConversationMetadata

headers = {"x-tr-product-name": "Westlaw", "x-tr-product-view": "Indigo"}

START_CONVERSATION_RESULT = StartConversationResponse(
    user_id="1234",
    conversation_id="conv_1234",
    conversation_entry_id="entry_345",
    conversation_metadata={"title": "title", "product": "pro", "data_retention": "1_DAY",
                           },
    conversation_entry_metadata={
        "email_notification": {
            "email_address": "<EMAIL>",
            "destination_url": "<EMAIL>",
        }
    }
)
payload = {
    "user_input": "input",
    "answer_solution_profile": "westlaw_chat_profile1",
    "intent_resolver_override": False,
    "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
    "conversation_metadata": {"title": "title", "product": "pro", "data_retention": "1_DAY",
                              },
    "conversation_entry_metadata": {
        "email_notification": {
            "email_address": "<EMAIL>",
            "destination_url": "<EMAIL>",
        }
    }
}


def create_test_request(headers=None, cookies=None):
    # Use the Headers class to create a valid headers object
    headers_object = Headers(headers or {})

    scope = {
        "type": "http",
        "some_other_scope_property": "value",
        "headers": headers_object.raw,  # Access the raw header items
        "cookies": cookies or {},
    }

    # Replace with actual scope properties
    request = Request(scope=scope)
    return request


class TestConversationRouter:

    def test_get_answer_solution_profile(self, mock_answer_profile_service_instance, client):
        answer_profile_data = {"name": "westlaw_chat_profile6", "default_fermi_jurisdiction": ["ALLCASES"],
                               "rag_solution": "wl-rag-v037",
                               "default_result_size": 1000, "sender_addr": "<EMAIL>",
                               "intent_profile": "wl-intent-resolver-v001",
                               "auto_submit_intent_classifications": ["legal", "illegal_information"],
                               "max_entries_per_conversation": 2,
                               "max_conversation_time_hours": 24, "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                               "supported_answer_content_types": ["CASE", "STATUTE", "REGULATION", "KNOWHOW",
                                                                  "ANALYTICAL"]}
        answer_profile = AnswerProfile(**answer_profile_data)
        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        mock_answer_profile_service_instance.all_profile_names.return_value = ["westlaw_chat_profile6"]
        response = client.get("/api/v2/conversation/answer-solution-profiles")
        assert response.status_code == status.HTTP_200_OK
        profile_name = "westlaw_chat_profile6"
        response = client.get(f"/api/v2/conversation/answer-solution-profiles?profile_name={profile_name}")
        assert response.status_code == status.HTTP_200_OK

    @patch('conversation_core.shared.services.profile_service.AnswerProfileService', autospec=True)
    def test_get_conversations(self, mock_profile_service, mock_conversation_db_instance, client):
        user_id = "156",
        mock_conversation_db_instance.get_conversations_metadata_by_user.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-product",
                    "profile": "x-tr-profile",
                    "title": "legal"
                }
            ]
        }

        response = client.get(f"/api/v1/conversation-list/{user_id}")
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('user_id') == '156'

        mock_conversation_db_instance.get_conversations_metadata_by_user.side_effect = DynamoDBNotFoundException(
            user_id,
            sort_key="15689")
        response = client.get(f"/api/v1/conversation-list/{user_id}")
        assert response.status_code == status.HTTP_404_NOT_FOUND

        mock_conversation_db_instance.get_conversations_metadata_by_user.side_effect = Exception("internal issue")
        response = client.get(f"/api/v1/conversation-list/{user_id}")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_get_conversation_list(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }

        response = client.get(f"/api/v2/conversation-list/{user_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('user_id') == '156'

    def test_get_conversation_list_filters(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }
        query_params = {
            'limit': 2,
            'page-offset': 3,
            'ascending-order': True,
            'is_favorite': 'true'
        }
        response = client.get(f"/api/v2/conversation-list/{user_id}", params=query_params, headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('user_id') == '156'

    def test_get_conversation_list_with_invalid_param(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        error_response = {"Error": {"Code": "ValidationException", "Message":
            "Invalid FilterExpression: Syntax error; token: \"_\", near: \"_fav\""}}

        mock_conversation_db_v2_instance.get_conversation_list.side_effect = ClientError(error_response, "Query")
        response = client.get(f"/api/v2/conversation-list/{user_id}?_fav=True", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })

        assert response.status_code == 400

        json_data = response.json()

        assert json_data.get(
            'detail') == ("An error occurred (ValidationException) when calling the Query "
                          "operation: Invalid FilterExpression: Syntax error; token: \"_\", near: \"_fav\"")

    def test_get_conversation_list_exception_404(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }

        mock_conversation_db_v2_instance.get_conversation_list.side_effect = DynamoDBNotFoundException(part_key=user_id,
                                                                                                       sort_key="45698725896")

        response = client.get(f"/api/v2/conversation-list/{user_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_conversation_list_exception_400_http(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }

        mock_conversation_db_v2_instance.get_conversation_list.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid value")
        response = client.get(f"/api/v2/conversation-list/{user_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_get_conversation_list_exception_400_value_error(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }

        mock_conversation_db_v2_instance.get_conversation_list.side_effect = ValueError("Invalid value")
        response = client.get(f"/api/v2/conversation-list/{user_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_get_conversation_list_exception_500(self, mock_conversation_db_v2_instance, client):
        user_id = "156"
        mock_conversation_db_v2_instance.get_conversation_list.return_value = {
            "user_id": "156",
            "conversations": [
                {
                    "conversation_id": "45698",
                    "additional_key_name": "add_key",
                    "additional_key_value": "add_value",
                    "created_date": 0,
                    "latest_entry_date": 0,
                    "product": "x-tr-blue",
                    "profile": "blue",
                    "title": "conv_title"
                }
            ]
        }
        mock_conversation_db_v2_instance.get_conversation_list.side_effect = Exception("Test error")
        response = client.get(f"/api/v2/conversation-list/{user_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_delete_conversation(self, mock_conversation_db_instance, client):
        mock_conversation_db_instance.reset_mock()
        user_id = "123"
        conversation_id = "456"
        mock_conversation_db_instance.delete_conversations.return_value = "delete_conversation_success"
        response = client.delete(f"/api/v1/conversation/{user_id}/{conversation_id}")
        assert response.status_code == 200

    def test_delete_conversation_exception(self, mock_conversation_db_instance, client):
        mock_conversation_db_instance.reset_mock()
        user_id = "123"
        conversation_id = "456"
        mock_conversation_db_instance.delete_conversations.return_value = "delete_conversation_failure"
        response = client.delete(f"/api/v1/conversation/{user_id}/{conversation_id}")
        assert response.status_code == 500

    def test_get_conversation(self, mock_conversation_db_instance, client):
        user_id = "test_user"
        conversation_id = "conversation_041"
        from conversation_core.shared.models.conversation import Conversation
        conversation = Conversation(user_id=user_id, conversation_id=conversation_id, conversation_history=[
            {
                'conversation_entry_id': 'conversation_entry_041',
                "status": "complete",
                "user_input": "test_user1",
                "email_address": "<EMAIL>",
                "send_email": False,
                "email_sent": True,
                "destination_url": "<EMAIL>",
            }
        ])
        mock_conversation_db_instance.get_conversation.return_value = conversation

        response = client.get(f"/api/v1/conversation/{user_id}/{conversation_id}")

        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('conversation_id') == 'conversation_041'
        assert json_data.get('user_id') == 'test_user'
        assert len(json_data.get('conversation_history')) == 1

        mock_conversation_db_instance.get_conversation.return_value = None
        response = client.get(f"/api/v1/conversation/{user_id}/{conversation_id}")
        assert response.status_code == 404

        mock_conversation_db_instance.get_conversation.side_effect = Exception("error message")
        response = client.get(f"/api/v1/conversation/{user_id}/{conversation_id}")
        assert response.status_code == 500

    def test_get_conversation_v2(self, mock_conversation_db_v2_instance, client):
        user_id = "test_user"
        conversation_id = "conversation_041"
        conversation = Conversation(user_id=user_id, conversation_id=conversation_id, conversation_metadata=None,
                                    conversation_entries=[
                                        {
                                            'conversation_entry_id': 'conversation_entry_041',
                                            "conversation_entry_metadata": {
                                                "user_classification": "user_classification"
                                            },
                                            "conversation_action_type": ConversationActionType.CHAT,
                                            "user_input": "test_user1",
                                            "status": RetrieveConversationEntryStatuses.INVALID_CONVERSATION_ENTRY_ID
                                        }
                                    ])
        mock_conversation_db_v2_instance.get_conversation.return_value = conversation

        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })

        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('conversation_id') == 'conversation_041'
        assert json_data.get('user_id') == 'test_user'

        mock_conversation_db_v2_instance.get_conversation.return_value = None
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 404

        mock_conversation_db_v2_instance.get_conversation.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail='Some error come')
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        mock_conversation_db_v2_instance.get_conversation.side_effect = Exception("error message")
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 500

    def test_get_conversation_db_failure(self, mock_conversation_db_v2_instance, client):
        user_id = "test_user"
        conversation_id = "conversation_041"
        conversation = None
        mock_conversation_db_v2_instance.get_conversation.return_value = conversation
        mock_conversation_db_v2_instance.get_conversation.side_effect = DynamoDBNotFoundException(user_id,
                                                                                                  conversation_id)
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 404

    def test_get_conversation_exception(self, mock_conversation_db_v2_instance, client):
        user_id = "test_user"
        conversation_id = "conversation_041"
        mock_conversation_db_v2_instance.get_conversation.side_effect = Exception("error message")
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        })
        assert response.status_code == 500

    def test_get_conversation_entry(self, mock_conversation_db_v2_instance, client):
        user_id = "test_user"
        conversation_id = "conv_145"
        conversation_entry_id = "conv_entry_123"
        conv_entry = ConversationEntry(conversation_entry_id=conversation_entry_id, conversation_entry_metadata={},
                                       conversation_action_type=ConversationActionType.CHAT,
                                       user_input="user_data",
                                       status=RetrieveConversationEntryStatuses.INVALID_CONVERSATION_ENTRY_ID)

        mock_conversation_db_v2_instance.get_conversation_entry.return_value = conv_entry
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              })

        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('conversation_entry_id') == 'conv_entry_123'

        mock_conversation_db_v2_instance.get_conversation_entry.side_effect = DynamoDBNotFoundException(user_id,
                                                                                                        conversation_id)
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              })
        assert response.status_code == status.HTTP_404_NOT_FOUND

        mock_conversation_db_v2_instance.get_conversation_entry.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Bad Request")
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              })
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        mock_conversation_db_v2_instance.get_conversation_entry.side_effect = Exception("error message")
        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              })
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_update_conversation_metadata(self, mock_conversation_db_instance, client):
        user_id = "user_test"
        conversation_id = "conv_145"
        payload = {
            "title": "title",
            "additional_key_name": "addi_key",
            "additional_key_value": "add_value"
        }

        obj = UpdateConversationMetadata(title="title", additional_key_name="addi_key",
                                         additional_key_value="add_value")
        mock_conversation_db_instance.update_metadata.return_value = obj
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/metadata", json=payload)
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('title') is not None

        mock_conversation_db_instance.update_metadata.return_value = False
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/metadata", json=payload)
        assert response.status_code == 404

        attributes = UpdateConversationMetadata()
        mock_conversation_db_instance.update_metadata.return_value = attributes
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/metadata", json={})
        assert response.status_code == 400

    def test_update_conversation_entry_metadata(self, mock_conversation_db_instance, client):
        user_id = "user_test"
        conversation_id = "conv_145"
        conversation_entry_id = "entry_123"
        from conversation_core.shared.constants import Constants
        mock_conversation_db_instance.retrieve_item.return_value = {
            Constants.CONV_RESULT_VIEWED_TIMESTAMP: {"N": "123456789"}
        }
        payload = {
            "email_address": "<EMAIL>",
            "destination_url": "<EMAIL>",
            "send_email": False,
            "result_viewed": False
        }
        from app.api.models.conversations.update_metadata_models import UpdateConversationEntryMetadata
        obj = UpdateConversationEntryMetadata(email_address="<EMAIL>", destination_url="<EMAIL>",
                                              send_email=False, result_viewed=False)
        mock_conversation_db_instance.update_entry_metadata.return_value = obj
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
                              json=payload)
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('email_address') == "<EMAIL>"

        mock_conversation_db_instance.update_entry_metadata.return_value = None
        mock_conversation_db_instance.update_entry_metadata.side_effect = HTTPException(status_code=404,
                                                                                        detail="Not Found")
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
                              json=payload)
        assert response.status_code == 404

    def test_update_conversation_entry_metadata_without_ts(self, mock_conversation_db_instance, client):
        user_id = "user_test_1"
        conversation_id = "conv_14"
        conversation_entry_id = "entry_12"
        mock_conversation_db_instance.retrieve_item.return_value = {}
        # Mock time.time() return value
        mock_conversation_db_instance.time.return_value = 987654321
        payload = {
            "email_address": "<EMAIL>",
            "destination_url": "<EMAIL>",
            "send_email": False,
            "result_viewed": True
        }
        from app.api.models.conversations.update_metadata_models import UpdateConversationEntryMetadata
        entry = UpdateConversationEntryMetadata(email_address="<EMAIL>", destination_url="<EMAIL>",
                                                send_email=False, result_viewed=False)
        mock_conversation_db_instance.update_entry_metadata.return_value = entry
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
                              json=payload)
        assert response.status_code == 200
        json_data = response.json()
        assert json_data.get('email_address') == "<EMAIL>"

    def test_update_conversation_entry_metadata_v2(self, mock_conversation_db_v2_instance, client):
        # IF
        user_id = "test_user"
        conversation_id = "conv_145"
        conversation_entry_id = "entry1"

        v2_result = ConversationEntryMetadata(email_notification={
            "email_address": "<EMAIL>",
            "send_email": False,
            "destination_url": "www.google.com",
            "email_sent": False
        }, custom={
            "result_viewed": {
                "is_result_viewed": False,
                "timestamp": 0
            },
            "feedback": {
                "is_result_helpful": False,
                "text_feedback": "good"
            }
        })
        mock_conversation_db_v2_instance.update_conversation_entry.return_value = v2_result
        mock_conversation_db_v2_instance.get_conversation_entry.return_value = MagicMock(
            conversation_entry_metadata=MagicMock(model_dump=MagicMock(return_value=v2_result.dict()))
        )

        payload = {
            "user_input": "Is it legal to drive high?",
            "answer_solution_profile": "westlaw_chat_profile3",
            "intent_resolver_override": False,
            "conversation_metadata": {
                "title": "Is it legal to drive high",
                "product": "WESTLAW",
                "created_timestamp": 0,
                "data_retention": "1_DAY",
                "custom": {
                    "client_id": "personal"
                }
            },
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "send_email": False,
                    "destination_url": "www.google.com",
                    "email_sent": False
                },
                "user_classification": "internal",
                "custom": {
                    "result_viewed": {
                        "is_result_viewed": False,
                        "timestamp": 0
                    },
                    "feedback": {
                        "is_result_helpful": False,
                        "text_feedback": "good"
                    }
                }
            }
        }
        _HEADERS = {
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        }
        # CASES
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata',
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)
        assert response.status_code == 200
        response_json = response.json()
        assert response_json.get('custom', {}).get('feedback', {}).get('text_feedback') == "good"
        # Test all other exception cases
        mock_conversation_db_v2_instance.get_conversation_entry.side_effect = Exception("error")
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata',
                              headers=_HEADERS, json=payload)
        assert response.status_code == 500
        # Test not updated case
        mock_conversation_db_v2_instance.update_conversation_entry.return_value = False
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata',
                              headers=_HEADERS, json=payload)
        assert response.status_code == status.HTTP_304_NOT_MODIFIED
        # Test entry not found case
        mock_conversation_db_v2_instance.update_conversation_entry.side_effect = DynamoDBNotFoundException(
            user_id, conversation_entry_id)
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata',
                              headers=_HEADERS, json=payload)
        assert response.status_code == 404

    def test_update_conversation_entry_metadata_v2_should_return_bad_request_for_forbidden_attributes(self,
                                                                                                      mock_conversation_db_v2_instance,
                                                                                                      client):
        user_id = "test_user"
        conversation_id = "conv_145"
        conversation_entry_id = "entry1"

        payload = {
            "email_notification": {
                "email_sent": False
            }
        }
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata',
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)
        assert response.status_code == 400
        assert response.json()["detail"] == "Attribute email_sent is forbidden to update."

    def test_update_conversation_metadata_v2(self, mock_conversation_db_v2_instance, client):
        conversation_id = 'conv_122'
        user_id = 'user_test'

        payload = {
            "title": "Is it legal to drive high",
            "product": "WESTLAW",
            'is_favorite': False,
        }
        v2_result = ConversationMetadata(**payload)
        mock_conversation_db_v2_instance.update_conversation.return_value = v2_result
        conversation = Conversation(user_id=user_id, conversation_id=conversation_id, conversation_metadata=payload,
                                    conversation_entries=[
                                        {
                                            'conversation_entry_id': 'conversation_entry_041',
                                            "conversation_entry_metadata": {
                                                "user_classification": "user_classification"
                                            },
                                            "conversation_action_type": ConversationActionType.CHAT,
                                            "user_input": "test_user1",
                                            "status": RetrieveConversationEntryStatuses.INVALID_CONVERSATION_ENTRY_ID
                                        }
                                    ])
        mock_conversation_db_v2_instance.get_conversation.return_value = conversation
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/metadata",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)
        assert response.status_code == 200
        response_json = response.json()
        assert response_json == payload

        mock_conversation_db_v2_instance.get_conversation.side_effect = Exception("error")
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/metadata',
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

        mock_conversation_db_v2_instance.get_conversation.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="error")
        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/metadata',
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_update_conversation_metadata_not_modified(self, mock_conversation_db_v2_instance, client):
        conversation_id = 'conv_122'
        user_id = 'user_test'
        payload = {
            "title": "Is it legal to drive high",
            "product": "PRACTICAL LAW",
            'is_favorite': False,
        }
        mock_conversation_db_v2_instance.get_conversation.return_value = MagicMock(
            conversation_metadata={"existing": "metadata"})
        mock_conversation_db_v2_instance.update_conversation.return_value = False

        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/metadata',
                              headers={"x-tr-product-name": "x-tr-product-name",
                                       "x-tr-product-view": "x-tr-product-view"},
                              json=payload)

        assert response.status_code == status.HTTP_304_NOT_MODIFIED

    def test_update_conversation_metadata_v2_conversation_id_not_found(self, client, mock_conversation_db_v2_instance):
        user_id = "test_user"
        conversation_id = "non_existent_conversation"

        mock_conversation_db_v2_instance.get_conversation.side_effect = DynamoDBNotFoundException(user_id,
                                                                                                  conversation_id)

        response = client.get(f"/api/v2/conversation/{user_id}/{conversation_id}",
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              }, )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json() == {"detail": f"No conversation details found for user_id : {user_id} and "
                                             f"conversation_id : {conversation_id}."}

    def test_update_conversation_metadata_v2_should_return_bad_request_for_forbidden_attributes(
            self,
            mock_conversation_db_v2_instance,
            client):
        conversation_id = 'conv_122'
        user_id = 'user_test'

        payload = {
            "profile": "profile",
            "created_timestamp": "1234567",
            "data_retention": "1_DAY",
            "latest_entry_date": "1234567",
            "latest_entry_status": "Complete",
            "max_followup_date": "1234567",
        }
        v2_result = ConversationMetadata(**payload)
        mock_conversation_db_v2_instance.update_conversation.return_value = v2_result
        conversation = Conversation(user_id=user_id, conversation_id=conversation_id, conversation_metadata=payload,
                                    conversation_entries=[
                                        {
                                            'conversation_entry_id': 'conversation_entry_041',
                                            "conversation_entry_metadata": {
                                                "user_classification": "user_classification"
                                            },
                                            "conversation_action_type": ConversationActionType.CHAT,
                                            "user_input": "test_user1",
                                            "status": RetrieveConversationEntryStatuses.INVALID_CONVERSATION_ENTRY_ID
                                        }
                                    ])
        mock_conversation_db_v2_instance.get_conversation.return_value = conversation

        response = client.put(f'/api/v2/conversation/{user_id}/{conversation_id}/metadata',
                              headers={
                                  "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
                              },
                              json=payload)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert all(attribute in response.json()["detail"] for attribute in payload.keys())

    def test_update_conversation_entry_metadata_failure(self, mock_conversation_db_instance, client):
        user_id = "user_test1"
        conversation_id = "conv_1451"
        conversation_entry_id = "entry_1231"
        mock_conversation_db_instance.retrieve_item.return_value = {
            'ResponseData': {
                'HTTPStatusCode': 200
            }
        }
        payload = {
            "email_address": "<EMAIL>",
            "destination_url": "<EMAIL>",
            "send_email": False,
            "result_viewed": False
        }
        mock_conversation_db_instance.update_entry_metadata.return_value = None
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
                              json=payload)
        assert response.status_code == 404

    def test_update_conversation_entry_metadata_http_exception(self, mock_conversation_db_instance, client):
        user_id = "user_test2"
        conversation_id = "conv_1452"
        conversation_entry_id = "entry_1232"
        payload = {
            "email_address": None,
            "destination_url": None,
            "send_email": None,
            "result_viewed": None
        }

        mock_conversation_db_instance.update_entry_metadata.return_value = HTTPException(status_code=400,
                                                                                         detail="Not valid")
        response = client.put(f"/api/v1/conversation/{user_id}/{conversation_id}/{conversation_entry_id}/metadata",
                              json=payload)
        assert response.status_code == 400

    @patch("services.conversation_creation_service.send_conversation_task_v2")
    @patch("services.conversation_creation_service.create_conversation_entry_v2", autospec=True)
    def test_start_conversation_entry_with_new_conversation_v2_200(self, mock_continue_conv_v2, mock_send_conv_task_v2,
                                                                   mock_answer_profile_service_instance, client):
        user_id = "1234"

        URL = f"/api/v2/conversation/{user_id}/entry"
        HEADERS = {"x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"}

        # Tests
        answer_profile = AnswerProfile(
            name="westlaw_chat_profile1",
            default_fermi_jurisdiction=["ALLCASES"],
            queue_name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )

        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        mock_continue_conv_v2.return_value = START_CONVERSATION_RESULT
        response = client.post(URL, headers=HEADERS, json=payload)
        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json.get('user_id') == user_id
        assert response_json.get('conversation_entry_id') is not None

    @patch("services.conversation_creation_service.create_conversation_entry_v2", autospec=True)
    def test_start_conversation_entry_with_new_conversation_v2_400(self, mock_continue_conv_v2,
                                                                   mock_answer_profile_service_instance, client):
        user_id = "1234"

        URL = f"/api/v2/conversation/{user_id}/entry"
        HEADERS = {"x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"}

        # Tests
        answer_profile = AnswerProfile(
            name="westlaw_chat_profile1",
            default_fermi_jurisdiction=["ALLCASES"],
            queue_name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )
        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        mock_continue_conv_v2.return_value = START_CONVERSATION_RESULT

        # Test 400_BAD_REQUEST response with incorrect request body
        response = client.post(URL, headers=HEADERS, json={"user_input": "input"})
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        response = client.post(URL, headers=HEADERS, json={"answer_solution_profile": "westlaw_chat_profile1"})
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        mock_continue_conv_v2.side_effect = HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail='error')
        response = client.post(URL, headers=HEADERS, json=payload)
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @patch("services.conversation_creation_service.create_conversation_entry_v2", autospec=True,
           side_effect=Exception('error'))
    def test_start_conversation_entry_with_new_conversation_v2_500(self, mock_continue_conv_v2,
                                                                   mock_answer_profile_service_instance, client):
        user_id = "1234"

        URL = f"/api/v2/conversation/{user_id}/entry"
        HEADERS = {"x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"}

        # Tests

        class MockProfile:
            queue_name: str = "westlaw_chat_profile1"

        mock_answer_profile_service_instance.get_profile.return_value = MockProfile()
        response = client.post(URL, headers=HEADERS, json=payload)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch("services.conversation_creation_service.send_conversation_task_v2")
    @patch("utils.validation_utils.validate_conversation_for_followup", autospec=True)
    @patch("services.conversation_creation_service.start_or_continue_conversation_v2", autospec=True)
    def test_start_conversation_entry_on_existing_conversation_v2(self, mock_existing_conv_v2,
                                                                  mock_validate_lock,
                                                                  mock_send_cov,
                                                                  mock_answer_profile_service_instance, client,
                                                                  global_mocks):
        user_id = "456987"
        conversation_id = "45698754"

        result = StartConversationResponse(
            user_id=user_id,
            conversation_id="45698754",
            conversation_entry_id="entry_345",
            conversation_metadata={"title": "title", "product": "pro", "data_retention": "1_DAY",
                                   },
            conversation_entry_metadata={
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "destination_url": "<EMAIL>",
                }}
        )

        payload = {
            "user_input": "input_2",
            "answer_solution_profile": "westlaw_chat_profile1",
            "intent_resolver_override": False,
            "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
            "conversation_metadata": {"title": "title", "product": "pro"},
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "destination_url": "<EMAIL>",
                }
            }
        }
        mock_validate_lock.return_value = None
        mock_existing_conv_v2.return_value = result
        answer_profile = AnswerProfile(
            name="westlaw_chat_profile1",
            default_fermi_jurisdiction=["ALLCASES"],
            queue_name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )
        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        conversation = Conversation()
        conv_entry = ConversationEntry()
        conv_entry.conversation_action_type = ConversationActionType.CHAT
        conversation.conversation_metadata = ConversationMetadata(
            **{"title": "title", "product": "pro", "data_retention": "1_DAY",
               "max_followup_date": -1})
        conv_entry.conversation_action_type = ConversationActionType.RAG
        conv_entry.conversation_entry_id = conversation_id
        conv_entry.timestamp = 1000000
        conv_entry.status = RetrieveConversationEntryStatuses.IN_PROGRESS
        conv_entry.result = ConversationEntryResult(answer_solution_profile="westlaw_chat_profile1",
                                                    conversation_action_type=ConversationActionType.RAG,
                                                    timestamp=100000,
                                                    system_output=None,
                                                    jurisdictions=["MN-CS", "MN-CS-ALL"]
                                                    )
        conversation.conversation_entries = [conv_entry]
        global_mocks["db_v2"].get_conversation.return_value = conversation
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/entry", headers={
            "x-tr-product-name": "x-tr-product-name", "x-tr-product-view": "x-tr-product-view"
        }, json=payload)
        assert response.status_code == 433
        response_json = response.json()
        assert response_json.get('detail') == "Conversation has an in-progress entry. Please wait until that entry has completed before submitting follow-ups"

    @patch("utils.validation_utils.validate_conversation_for_followup", autospec=True)
    @patch("utils.validation_utils.validate_if_previous_conv_in_progress", autospec=True)
    def test_start_conversation_entry_on_existing_conversation_v2_throws_exception_conv_locked(self,
                                                                                               mock_validate_if_previous_conv_in_progress,
                                                                                               mock_validate_conversation_for_followup,
                                                                                               client, global_mocks,
                                                                                               mock_answer_profile_service_instance):
        user_id = "456987"
        conversation_id = "45698754"
        payload = {
            "user_input": "input_2",
            "answer_solution_profile": "westlaw_chat_profile6",
            "intent_resolver_override": False,
            "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
            "conversation_metadata": {"title": "title", "product": "pro"},
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "destination_url": "<EMAIL>",
                }
            }
        }
        answer_profile = AnswerProfile(
            name="westlaw_chat_profile6",
            default_fermi_jurisdiction=["ALLCASES"],
            queue_name="westlaw_chat_profile6",
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )
        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        conversation = Conversation()
        conversation.conversation_id = conversation_id
        conv_entry = ConversationEntry()
        conv_entry.conversation_action_type = ConversationActionType.CHAT
        conversation.conversation_metadata = ConversationMetadata(
            **{"title": "title", "product": "pro", "data_retention": "1_DAY",
               "max_followup_date": 4})
        conv_entry.conversation_action_type = ConversationActionType.RAG
        conv_entry.conversation_entry_id = conversation_id
        conv_entry.timestamp = 1000000
        conv_entry.status = RetrieveConversationEntryStatuses.IN_PROGRESS
        conv_entry.result = ConversationEntryResult(answer_solution_profile="westlaw_chat_profile6",
                                                    conversation_action_type=ConversationActionType.RAG,
                                                    timestamp=100000,
                                                    system_output=None,
                                                    jurisdictions=["MN-CS", "MN-CS-ALL"]
                                                    )
        conversation.conversation_entries = [conv_entry]
        global_mocks["db_v2"].get_conversation.return_value = conversation
        error_msg = f"Conversation {conversation.conversation_id} has exceeded the max hours allowed since " \
                    f"creation of conversation"

        mock_validate_conversation_for_followup.side_effect = HTTPException(status_code=434, detail=error_msg)
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/entry", json=payload, headers=headers)
        assert response.status_code == 434
        assert response.json().get('detail') == error_msg

    @patch("utils.validation_utils.validate_conversation_for_followup", autospec=True)
    @patch("utils.validation_utils.validate_if_previous_conv_in_progress", autospec=True)
    def test_start_conversation_entry_on_existing_conversation_v2_throws_exception_conv_locked_404(self,
                                                                                                   mock_validate_if_previous_conv_in_progress,
                                                                                                   mock_validate_conversation_for_followup,
                                                                                                   client,
                                                                                                   global_mocks,
                                                                                                   mock_answer_profile_service_instance):
        user_id = "456987"
        conversation_id = "12345"
        payload = {
            "user_input": "input_2",
            "answer_solution_profile": "westlaw_chat_profile6",
            "intent_resolver_override": False,
            "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
            "conversation_metadata": {"title": "title", "product": "pro"},
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "destination_url": "<EMAIL>",
                }
            }
        }
        answer_profile = AnswerProfile(
            name="westlaw_chat_profile6",
            default_fermi_jurisdiction=["ALLCASES"],
            queue_name="westlaw_chat_profile6",
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL,
                                                IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )
        mock_answer_profile_service_instance.get_profile.return_value = answer_profile

        global_mocks["db_v2"].get_conversation.return_value = None

        error_msg = f"Conversation with conversationId {conversation_id} for userId {user_id} was not found."
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/entry", json=payload, headers=headers)
        assert response.status_code == 404
        assert response.json().get('detail') == error_msg

    @patch("utils.validation_utils.validate_if_previous_conv_in_progress", autospec=True)
    def test_start_conversation_entry_on_existing_conversation_v2_throws_exception_conv_in_progress(self,
                                                                                                    mock_validate_if_previous_conv_in_progress,
                                                                                                    mock_answer_profile_service_instance,
                                                                                                    client,
                                                                                                    global_mocks):
        user_id = "456987"
        conversation_id = "45698754"
        payload = {
            "user_input": "input_2",
            "answer_solution_profile": "westlaw_chat_profile6",
            "intent_resolver_override": False,
            "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
            "conversation_metadata": {"title": "title", "product": "pro", "data_retention": "1_DAY",
                                      },
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "jane@sample-doc",
                    "send_email": False,
                    "destination_url": ""
                }
            }
        }

        answer_profile = AnswerProfile(
            name="westlaw_chat_profile6",
            default_fermi_jurisdiction=["ALLCASES"],
            rag_solution="wl-rag-v037",
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 2,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields_key": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )

        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        conversation = Conversation()
        conversation.conversation_metadata = ConversationMetadata(
            **{"title": "title", "product": "pro", "data_retention": "1_DAY",
               "max_followup_date": -1})
        conv_entry = ConversationEntry()
        conv_entry.conversation_action_type = ConversationActionType.RAG

        conv_entry.conversation_entry_id = '1232'
        conv_entry.timestamp = 1000000
        conv_entry.result = ConversationEntryResult(answer_solution_profile="westlaw_chat_profile6",
                                                    conversation_action_type=ConversationActionType.RAG,
                                                    timestamp=100000,
                                                    system_output=None,
                                                    jurisdictions=None
                                                    )
        conversation.conversation_entries = [conv_entry]
        global_mocks["db_v2"].get_conversation.return_value = conversation

        error_msg = f"Conversation has an in-progress entry. Please wait until that entry has completed before " \
                    f"submitting follow-ups"
        mock_validate_if_previous_conv_in_progress.side_effect = HTTPException(433, error_msg)
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/entry", json=payload, headers=headers)
        assert response.status_code == 433
        assert response.json().get('detail') == error_msg


    @patch("utils.validation_utils.validate_if_previous_conv_in_progress", autospec=True, return_value = True)
    def test_start_conversation_entry_on_existing_conversation_v2_throws_exception_max_entries_per_conv(self,
                                                                                                        mock_validate_if_previous_conv_in_progress,
                                                                                                        mock_answer_profile_service_instance,
                                                                                                        client,
                                                                                                        global_mocks):
        user_id = "456987"
        conversation_id = "45698754"
        payload = {
            "user_input": "input_2",
            "answer_solution_profile": "westlaw_chat_profile1",
            "intent_resolver_override": False,
            "jurisdictions_override": ["MN-CS", "MN-CS-ALL"],
            "content_types_override": ["CASE", "STATUTE", "REGULATION"],
            "conversation_metadata": {"title": "title", "product": "pro", "data_retention": "1_DAY",
                                      },
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "destination_url": "<EMAIL>",
                },
                "user_classification": "user",
                # "custom": {
                #     "result_viewed": {
                #         "is_result_viewed": False,
                #     },
                #     "feedback": {
                #         "text_feedback": "good"
                #     }
                # }
            }
        }

        answer_profile = AnswerProfile(
            name="westlaw_chat_profile1",
            default_fermi_jurisdiction=["ALLCASES"],
            rag_solution="wl-rag-v037",
            default_content_types=["CASE", "STATUTE", "REGULATION"],
            default_result_size=1000,
            sender_addr="<EMAIL>",
            intent_profile="wl-intent-resolver-v001",
            auto_submit_intent_classifications=[IntentClassification.LEGAL, IntentClassification.ILLEGAL_INFORMATION],
            additionalProfileMeta={"max_entries_per_conversation": 1,
                                   "max_conversation_time_hours": 24,
                                   "allowed_meta_data_fields": "WL_META_DATA_FIELDS",
                                   "supported_answer_content_types": ['CASE', 'STATUTE', 'REGULATION', 'KNOWHOW',
                                                                      'ANALYTICAL']}

        )

        mock_answer_profile_service_instance.get_profile.return_value = answer_profile
        conversation = Conversation()
        conversation.conversation_id = conversation_id
        conversation.conversation_metadata = ConversationMetadata(
            **{"title": "title", "product": "pro", "data_retention": "1_DAY",
               "max_followup_date": -1})
        conv_entry = ConversationEntry
        conv_entry.conversation_action_type = ConversationActionType.RAG
        conv_entry.conversation_entry_id = '1232'
        conv_entry.timestamp = 1000000
        conv_entry.result = ConversationEntryResult(answer_solution_profile="westlaw_chat_profile1",
                                                    conversation_action_type=ConversationActionType.RAG,
                                                    timestamp=100000,
                                                    system_output=None,
                                                    jurisdictions=None
                                                    )

        conversation.conversation_entries = [conv_entry,conv_entry ]
        global_mocks["db_v2"].get_conversation.return_value = conversation

        error_msg = (
            f"Conversation {conversation_id} has exceeded the max entries allowed for a conversation with profile "
            f"westlaw_chat_profile1")
        response = client.put(f"/api/v2/conversation/{user_id}/{conversation_id}/entry", json=payload, headers=headers)
        assert response.status_code == 432
        assert response.json().get('detail') == error_msg

    @patch("conversation_core.shared.models.answer_profile.AnswerProfile.intent_enabled")
    @patch("app.services.conversation_creation_service.send_conversation_task_v2")
    @patch("app.services.conversation_creation_service.send_conversation_task")
    @patch("app.services.conversation_creation_service.evaluate_intent")
    @patch("app.services.conversation_creation_service.remap_profile")
    def test_create_conversation_entry(self, mock_remap_profile, mock_evaluate_intent, mock_send_conversation_task,
                                       mock_send_conversation_task_v2, mock_chat_intent):
        is_new_conversation = True
        user_id = 'user123'
        start_answer_generation_request = StartAnswerGenerationRequest(user_input="how are you?",
                                                                       answer_solution_profile="practical_chat_profile1"
                                                                       )
        conversation_id = 'conv_123'
        auth_token = 'Bearer Token'
        cobalt_session = {'key': 'value'}
        request = create_test_request(headers={'header_key': 'header_value'}, cookies={'cookie_key': 'cookie_value'})
        email_address = '<EMAIL>'
        send_email = True
        destination_url = 'https://example.com'
        conversation_type = 'conv_type'
        run_intent_resolver = False
        auto_submit_task = True
        conversation_version = 1
        user_classification = 'unknown'

        mock_remap_profile.return_value = "practical_chat_profile1"

        from app.services.conversation_creation_service import create_conversation_entry
        create_conversation_entry(
            is_new_conversation, user_id, start_answer_generation_request, conversation_id, auth_token,
            cobalt_session, request, email_address, send_email, destination_url, run_intent_resolver,
            auto_submit_task, conversation_version, user_classification
        )
        mock_remap_profile.assert_called_once_with('PRACTICAL_CHAT_PROFILE1')

        mock_chat_intent.return_value = True
        v1_result = create_conversation_entry(
            is_new_conversation, user_id, start_answer_generation_request, conversation_id, auth_token,
            cobalt_session, request, email_address, send_email, destination_url, conversation_type,
            run_intent_resolver=True, auto_submit_task=True, conversation_version=1, user_classification="unknown"
        )
        assert v1_result is not None

        v2_result = create_conversation_entry(
            is_new_conversation, user_id, start_answer_generation_request, conversation_id, auth_token,
            cobalt_session, request, email_address, send_email, destination_url, conversation_type,
            run_intent_resolver=False, auto_submit_task=True, conversation_version=2, user_classification="unknown"
        )
        assert v2_result is not None

    @patch("conversation_core.shared.models.answer_profile.AnswerProfile.intent_enabled")
    @patch("app.services.conversation_creation_service.send_conversation_task")
    @patch("app.services.conversation_creation_service.remap_profile")
    def test_create_conversation_entry_follow_up(self, mock_remap_profile, mock_send_conversation_task,
                                                 mock_chat_intent):
        is_new_conversation = False
        user_id = 'user12'
        start_answer_generation_request = StartAnswerGenerationRequest(user_input="how are you??",
                                                                       answer_solution_profile="practical_chat_profile1"
                                                                       )
        conversation_id = 'conv_23'
        auth_token = 'Bearer_Token'
        cobalt_session = {'key1': 'value'}
        request = create_test_request(headers={'header_key': 'header_value'}, cookies={'cookie_key': 'cookie_value'})
        email_address = '<EMAIL>'
        send_email = True
        destination_url = 'https:example.com'
        conversation_type = "followup"
        run_intent_resolver = False
        auto_submit_task = True
        conversation_version = 1
        user_classification = 'unknown'

        mock_remap_profile.return_value = "practical_chat_profile1"

        from app.services.conversation_creation_service import create_conversation_entry
        create_conversation_entry(
            is_new_conversation, user_id, start_answer_generation_request, conversation_id, auth_token,
            cobalt_session, request, email_address, send_email, destination_url, conversation_type, run_intent_resolver,
            auto_submit_task, conversation_version, user_classification
        )
        mock_remap_profile.assert_called_once_with('PRACTICAL_CHAT_PROFILE1')

    @patch("app.services.conversation_creation_service.evaluate_intent")
    @patch("app.services.conversation_creation_service.send_conversation_task_v2")
    def test_create_conversation_entry_v2(self, mock_send_conversation_task_v2, mock_evaluate_intent):
        is_new_conversation = True
        auth_token = "bearer token"
        user_session = {"task_1": "rag"}
        request = create_test_request(headers={'header_key': 'header_value'}, cookies={'cookie_key': 'cookie_value'})
        user_id = "1458"
        conversation_id = "45697"
        user_input = "how about that?"
        profile = "practical_chat_profile1"
        jurisdictions_override = ["MN-CS", "MN-CS-ALL"]
        content_types_override = ["CASE", "STATUTE", "REGULATION", "KNOWHOW", "ANALYTICAL"]
        product = Constants.SESSION_PRODUCT_NAME
        title = "remap"
        from conversation_core.shared.models.v2.conversation import CustomMetadata
        custom_conversation_metadata = CustomMetadata(client_id="123")
        email_notification = EmailNotification(email_address="<EMAIL>", send_email=False,
                                               destination_url="<EMAIL>", email_sent=False)
        from conversation_core.shared.models.v2.conversation_entry import CustomMetadata
        custom_conversation_entry_metadata = CustomMetadata(result_viewed=None, feedback=None)

        from app.services.conversation_creation_service import create_conversation_entry_v2
        result = create_conversation_entry_v2(is_new_conversation=is_new_conversation, auth_token=auth_token,
                                              user_session=user_session, request=request, user_id=user_id,
                                              conversation_id=conversation_id,
                                              user_input=user_input, profile=profile,
                                              jurisdictions_override=jurisdictions_override,
                                              content_types_override=content_types_override,
                                              content_types_exclude=[],
                                              product=product,
                                              title=title,
                                              custom_conversation_metadata=custom_conversation_metadata,
                                              email_notification=email_notification,
                                              custom_conversation_entry_metadata=custom_conversation_entry_metadata,
                                              conversation_type="conv_type",
                                              run_intent_resolver=True,
                                              auto_submit_task=True,
                                              user_classification="classification")
        assert result is not None
        result_v2 = create_conversation_entry_v2(is_new_conversation=is_new_conversation, auth_token=auth_token,
                                                 user_session=user_session, request=request, user_id=user_id,
                                                 conversation_id=conversation_id,
                                                 user_input=user_input, profile=profile,
                                                 jurisdictions_override=jurisdictions_override,
                                                 content_types_override=content_types_override,
                                                 content_types_exclude=[],
                                                 product=product,
                                                 title=title,
                                                 custom_conversation_metadata=custom_conversation_metadata,
                                                 email_notification=email_notification,
                                                 custom_conversation_entry_metadata=custom_conversation_entry_metadata,
                                                 conversation_type="conv_type",
                                                 run_intent_resolver=False,
                                                 auto_submit_task=True,
                                                 user_classification="classification")
        assert result_v2 is not None
        result_is_new_conv_false = create_conversation_entry_v2(is_new_conversation=False, auth_token=auth_token,
                                                                user_session=user_session, request=request,
                                                                user_id=user_id,
                                                                conversation_id=conversation_id,
                                                                user_input=user_input, profile=profile,
                                                                jurisdictions_override=jurisdictions_override,
                                                                content_types_override=content_types_override,
                                                                content_types_exclude=[],
                                                                product=product,
                                                                title=title,
                                                                custom_conversation_metadata=custom_conversation_metadata,
                                                                email_notification=email_notification,
                                                                custom_conversation_entry_metadata=custom_conversation_entry_metadata,
                                                                conversation_type="conv_type",
                                                                run_intent_resolver=False,
                                                                auto_submit_task=True,
                                                                user_classification="classification")
        assert result_is_new_conv_false is not None

    @patch("app.services.conversation_creation_service.remap_profile")
    @patch("app.services.conversation_creation_service.send_summarize_task")
    def test_create_summarize_conversation_entry(self, mock_send_summarize_task, mock_remap_profile):
        user_id = "1235"
        conversation_id = "45689"
        auth_token = "bearer token"
        cobalt_session = {"session_1": "cobalt"}
        request = create_test_request(headers={'header_key': 'header_value'}, cookies={'cookie_key': 'cookie_value'})
        user_classification = "test_classification"

        summarize_answer_generation_request = SummarizeAnswerGenerationRequest(
            legacy_id="legacy_1",
            headnote_id="headnote_1",
            citing_case_ids=["westlaw_keycite_profile1"],
            answer_solution_profile="practical_chat_profile1"
        )
        mock_remap_profile.return_value = "practical_chat_profile1"
        from app.services.conversation_creation_service import create_summarize_conversation_entry
        result = create_summarize_conversation_entry(
            user_id=user_id, conversation_id=conversation_id, auth_token=auth_token, cobalt_session=cobalt_session,
            request=request, user_classification=user_classification,
            summarize_answer_generation_request=summarize_answer_generation_request
        )
        assert hasattr(result, 'user_id')
        mock_remap_profile.assert_called_once_with("PRACTICAL_CHAT_PROFILE1")

    @patch("conversation_core.shared.models.answer_profile.AnswerProfile.intent_enabled")
    @patch("app.services.conversation_creation_service.evaluate_intent")
    @patch("app.services.conversation_creation_service.send_conversation_task_v2")
    def test_trigger_conversation_task(self, mock_send_conv_v2, mock_intent,
                                       mock_intent_chat_profile):
        mock_intent_chat_profile.return_value = True
        conversation_request = StartConversationRequest(user_input="how are you?",
                                                        answer_solution_profile="practical_chat_profile1",
                                                        jurisdictions_override=["MN-CS", "MN-CS-ALL"],
                                                        content_types_override=["CASE", "STATUTE", "REGULATION",
                                                                                "KNOWHOW", "ANALYTICAL"])

        conversation_response = StartConversationResponse(user_id="12356", conversation_id="45789",
                                                          conversation_entry_id="4875")
        from app.services.conversation_creation_service import trigger_conversation_task
        trigger_conversation_task(auth_token="bearer token", auto_submit_task=True,
                                  cobalt_session={"task_1": "rag"},
                                  conversation_version=1,
                                  is_new_conversation=True,
                                  request=create_test_request(headers={'header_key': 'header_value'},
                                                              cookies={'cookie_key': 'cookie_value'}),
                                  run_intent_resolver=True, start_conversation_request=conversation_request,
                                  start_conversation_response=conversation_response, user_id="12356",
                                  user_classification="classification")

        expected_value = mock_intent(is_new_conversation=True,
                                     user_id='evaluate_intent',
                                     user_input="How are you?",
                                     answer_solution_profile="practical_chat_profile1",
                                     jurisdictions_override=["MN-CS", "MN-CS-ALL"],
                                     content_types_override=["CASE", "STATUTE", "REGULATION", "KNOWHOW", "ANALYTICAL"],
                                     conversation_id='45689',
                                     conversation_entry_id='789456',
                                     conversation_action_type=ConversationActionType.RAG,
                                     auth_token='Bearer_token',
                                     meta_data={"green": "route_to_green", "user_classification": "unknown"},
                                     user_session=None)
        assert expected_value is not None

        trigger_conversation_task(auth_token="bearer token", auto_submit_task=True,
                                  cobalt_session={"task_1": "rag"},
                                  conversation_version=2,
                                  is_new_conversation=True,
                                  request=create_test_request(headers={'header_key': 'header_value'},
                                                              cookies={'cookie_key': 'cookie_value'}),
                                  run_intent_resolver=False, start_conversation_request=conversation_request,
                                  start_conversation_response=conversation_response, user_id="12356",
                                  user_classification="classification")

        expected_value = mock_send_conv_v2(is_new_conversation=True,
                                           user_id='evaluate_intent',
                                           user_input="How are you?",
                                           answer_solution_profile="practical_chat_profile1",
                                           jurisdictions_override=["MN-CS", "MN-CS-ALL"],
                                           content_types_override=["CASE", "STATUTE", "REGULATION", "KNOWHOW",
                                                                   "ANALYTICAL"],
                                           conversation_id='45689',
                                           conversation_entry_id='789456',
                                           conversation_action_type=ConversationActionType.RAG,
                                           auth_token='Bearer_token',
                                           meta_data={"green": "route_to_green", "user_classification": "unknown"},
                                           user_session=None)
        assert expected_value is not None

    def test_get_answer_solution_profiles(self, mock_answer_profile_service_instance, client):
        # Arrange
        mock_answer_profile_service_instance.all_profile_names.return_value = ['profile1', 'profile2']
        mock_answer_profile_service_instance.get_profile.return_value = AnswerProfile()

        # # Test empty params
        # response = client.get(f"/api/v2/conversation/answer-solution-profiles")
        # assert response.status_code == 200
        # json_data = response.json()
        # assert len(json_data) > 1

        # Test correct params
        params = dict(profile_name="profile1")
        response = client.get(f"/api/v2/conversation/answer-solution-profiles", params=params)
        assert response.status_code == 200
        json_data = response.json()
        assert len(json_data) == 1

        # Test incorrect params
        mock_answer_profile_service_instance.get_profile.side_effect = NameError()
        params = dict(profile_name="un-existing-profile")
        response = client.get(f"/api/v2/conversation/answer-solution-profiles", params=params)
        assert response.status_code == 404
        mock_answer_profile_service_instance.get_profile.side_effect = ProfileNotFoundException()
        params = dict(profile_name="un-existing-profile")
        response = client.get(f"/api/v2/conversation/answer-solution-profiles", params=params)
        assert response.status_code == 404

    @patch("app.services.conversation_creation_service.send_summarize_task")
    def test_create_summarize_conversation_entry_v2(self, mock_send_summarize_task):
        request = create_test_request(headers={'header_key': 'header_value'}, cookies={'cookie_key': 'cookie_value'})
        auth_token = "Bearer Token"
        cobalt_session = {"key": "value"}
        profile = "practical_chat_profile1"
        user_id = "user123"
        headnote_id = "headnote1234"
        legacy_id = "legacy123"
        citing_case_ids = ["case1", "case2"]
        product = "product123"
        user_classification = "user_classification"
        data_retention = DataRetention.ONE_DAY
        obj_metadata = ConversationMetadata(title="title", product="pro", data_retention="1_DAY",
                                            custom={"client_id": "2345"})
        conversation_metadata = obj_metadata
        obj_email = EmailNotification(email_address="<EMAIL>", send_email=False,
                                      destination_url="<EMAIL>",
                                      email_sent=False)
        obj_entry_meta = ConversationEntryMetadata(email_notification=obj_email, custom={
            "result_viewed": {
                "is_result_viewed": False,
                "result_viewed_timestamp": 0
            },
            "feedback": {
                "is_result_helpful": False,
                "text_feedback": "good"
            }
        })
        conversation_entry_metadata = obj_entry_meta

        from app.services.conversation_creation_service import create_summarize_conversation_entry_v2
        result = create_summarize_conversation_entry_v2(
            self, request, auth_token, cobalt_session, profile, user_id, headnote_id, legacy_id,
            citing_case_ids, product, user_classification, data_retention,
            conversation_metadata, conversation_entry_metadata
        )

        assert result.user_id == "user123"
        assert result.conversation_id is not None
        assert result.conversation_entry_id is not None

    def test_summarize_conversation_key_cite_v1(self, client):
        user_id = "1587"

        payload = {
            "answer_solution_profile": "westlaw_keycite_profile1",
            "headnote_id": "1",
            "legacy_id": "2",
            "citing_case_ids": [
                "ras", "rag"
            ],
            "conversation_metadata": {
                "title": "ras",
                "product": "x-tr-1",
                "created_timestamp": 0,
                "data_retention": "1_DAY",
                "custom": {
                    "client_id": "user"
                }
            },
            "conversation_entry_metadata": {
                "email_notification": {
                    "email_address": "<EMAIL>",
                    "send_email": False,
                    "destination_url": "<EMAIL>",
                    "email_sent": False
                },
                "custom": {
                    "result_viewed": {
                        "is_result_viewed": False,
                        "timestamp": 0
                    },
                    "feedback": {
                        "is_result_helpful": False,
                        "text_feedback": "good"
                    }
                }
            }
        }

        response = client.post(f"/api/v1/summarize/{user_id}/keycite", json=payload)
        assert response.status_code == 200
        response_json = response.json()
        assert response_json.get('user_id') == user_id
        assert response_json.get('conversation_id') is not None
        assert response_json.get('conversation_entry_id') is not None

    @patch('app.services.conversation_creation_service.answer_profile_service')
    def test_append_disclaimer_default(self, mock_answer_profile_service_instance):
        entry: ConversationEntry = ConversationEntry(
            conversation_entry_id="entry_123",
            conversation_entry_metadata=ConversationEntryMetadata(),
            conversation_action_type=ConversationActionType.RAG,
            status=RetrieveConversationEntryStatuses.COMPLETE,
            result=ConversationEntryResult(
                jurisdictions=["MN-CS"],
                timestamp=100000,
                answer_solution_profile="westlaw_chat_profile1",
                system_output={
                    "response": "Yep, that's legal."
                },
                conversation_action_type=ConversationActionType.RAG,
            ).dict()
        )
        profile: AnswerProfile = AnswerProfile(
            name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
        )
        mock_answer_profile_service_instance.get_profile.return_value = profile
        from app.services.conversation_creation_service import append_disclaimer
        append_disclaimer(entry, profile_name="westlaw_chat_profile1")
        assert (entry.result.system_output.get("response") ==
                "Yep, that's legal." + ConversationConstants.LLM_AI_DEFAULT_DISCLAIMER)

    @patch('app.services.conversation_creation_service.answer_profile_service.get_profile')
    def test_append_disclaimer_custom_text_custom_key(self, mock_get_profile):
        entry: ConversationEntry = ConversationEntry(
            conversation_entry_id="entry_123",
            conversation_entry_metadata=ConversationEntryMetadata(),
            conversation_action_type=ConversationActionType.RAG,
            status=RetrieveConversationEntryStatuses.COMPLETE,
            result=ConversationEntryResult(
                jurisdictions=["MN-CS"],
                timestamp=100000,
                answer_solution_profile="westlaw_chat_profile1",
                system_output={
                    "ai_output": "Yep, that's legal."
                },
                conversation_action_type=ConversationActionType.RAG,
            ).dict()
        )
        profile: AnswerProfile = AnswerProfile(
            name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            additionalProfileMeta={
                "disclaimer_override_text": "\n\nNote: might not be legal.",
                "system_output_key": "ai_output"},
        )
        mock_get_profile.return_value = profile
        from app.services.conversation_creation_service import append_disclaimer
        append_disclaimer(entry, profile_name="westlaw_chat_profile1")
        assert (entry.result.system_output.get("ai_output") ==
                "Yep, that's legal.\n\nNote: might not be legal.")

    @patch('app.services.conversation_creation_service.answer_profile_service.get_profile')
    def test_append_disclaimer_html(self, mock_get_profile):
        entry: ConversationEntry = ConversationEntry(
            conversation_entry_id="entry_123",
            conversation_entry_metadata=ConversationEntryMetadata(),
            conversation_action_type=ConversationActionType.RAG,
            status=RetrieveConversationEntryStatuses.COMPLETE,
            result=ConversationEntryResult(
                jurisdictions=["MN-CS"],
                timestamp=100000,
                answer_solution_profile="westlaw_chat_profile1",
                system_output={
                    "response": "<h1>AI Generated Response</h1><br/><p>Yep, that's legal.</p>"
                },
                conversation_action_type=ConversationActionType.RAG,
            ).dict()
        )
        profile: AnswerProfile = AnswerProfile(
            name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            additionalProfileMeta={"disclaimer_use_html": True,
                                   "disclaimer_override_text": "\n\nTake this answer with a grain of salt."},
        )
        mock_get_profile.return_value = profile
        from app.services.conversation_creation_service import append_disclaimer
        append_disclaimer(entry, profile_name="westlaw_chat_profile1")
        assert (entry.result.system_output.get("response") ==
                "<h1>AI Generated Response</h1><br/><p>Yep, that's legal.</p>" +
                "<p><br/><br/>Take this answer with a grain of salt.</p>")

    @patch('app.services.conversation_creation_service.answer_profile_service.get_profile')
    def test_append_disclaimer_no_disclaimer(self, mock_get_profile):
        entry: ConversationEntry = ConversationEntry(
            conversation_entry_id="entry_123",
            conversation_entry_metadata=ConversationEntryMetadata(),
            conversation_action_type=ConversationActionType.RAG,
            status=RetrieveConversationEntryStatuses.COMPLETE,
            result=ConversationEntryResult(
                jurisdictions=["MN-CS"],
                timestamp=100000,
                answer_solution_profile="westlaw_chat_profile1",
                system_output={
                    "response": "Yep, that's legal."
                },
                conversation_action_type=ConversationActionType.RAG,
            ).dict()
        )
        profile: AnswerProfile = AnswerProfile(
            name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            additionalProfileMeta={"include_disclaimer": False},
        )
        mock_get_profile.return_value = profile
        from app.services.conversation_creation_service import append_disclaimer
        append_disclaimer(entry, profile_name="westlaw_chat_profile1")
        assert entry.result.system_output.get("response") == "Yep, that's legal."

    @patch('app.services.conversation_creation_service.answer_profile_service.get_profile')
    def test_append_disclaimer_no_system_output_key(self, mock_get_profile):
        result = ConversationEntryResult(
            answer_solution_profile="westlaw_chat_profile1",
            conversation_action_type=ConversationActionType.RAG,
            jurisdictions=["MN-CS"],
            timestamp=100000,
            system_output={
                "summary_text": "Yep, that's legal."
            },
        )
        entry: ConversationEntry = ConversationEntry(
            conversation_entry_id="entry_123",
            conversation_entry_metadata=ConversationEntryMetadata(),
            conversation_action_type=ConversationActionType.RAG,
            status=RetrieveConversationEntryStatuses.COMPLETE,
            result=result.dict(),
        )
        profile: AnswerProfile = AnswerProfile(
            name="westlaw_chat_profile1",
            rag_solution="wl-rag-v037",
            additionalProfileMeta={},
        )
        mock_get_profile.return_value = profile
        from app.services.conversation_creation_service import append_disclaimer
        append_disclaimer(entry, profile_name="westlaw_chat_profile1")
        assert entry.result.system_output.get("disclaimer") == ConversationConstants.LLM_AI_DEFAULT_DISCLAIMER


if __name__ == "__main__":
    unittest.main()
