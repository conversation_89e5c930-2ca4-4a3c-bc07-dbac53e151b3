import os
from unittest.mock import patch
from fastapi import status


class TestHealthCheckRouter:

    @patch("core.health_service.HealthService.should_terminate")
    def test_liveness(self, mock_terminate_service, client):
        os.environ["HOSTNAME"] = "test_pod"
        mock_terminate_service.return_value = False
        response = client.get("/actuator/health/liveness")
        assert response.status_code == 200
        assert response.json() == {"status": "UP", "pod": "test_pod", "message": "Container is healthy."}

        mock_terminate_service.return_value = True
        response = client.get("/actuator/health/liveness")
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        assert response.json() == {"status": "DOWN", "pod": "test_pod", "message": "Container is in a bad state."}

        del os.environ["HOSTNAME"]

    @patch("core.health_service.HealthService.check_memory")
    def test_readiness(self, mock_check_memory, client):
        os.environ["HOSTNAME"] = "test_pod"
        mock_check_memory.return_value = True
        response = client.get("/actuator/health/readiness")
        assert response.status_code == 200
        assert response.json() == {"status": "UP", "pod": "test_pod", "message": "Container is ready."}

        mock_check_memory.return_value = False
        response = client.get("/actuator/health/readiness")
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        assert response.json() == {"status": "DOWN", "pod": "test_pod", "message": "Container is not ready."}

        del os.environ["HOSTNAME"]

    def test_actuator(self, client):
        response = client.get("/actuator/health")

        assert response.status_code == 200
        assert response.json() == {"status": "UP", " groups": ["liveness", "readiness"]}
