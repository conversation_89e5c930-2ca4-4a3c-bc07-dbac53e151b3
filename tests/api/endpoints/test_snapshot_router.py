import time
import unittest
from unittest.mock import patch, MagicMock

import pytest
from app.config.settings import Settings

class TestSnapshotRouter:

    @pytest.mark.asyncio
    @patch('app.config.settings.get_settings')
    @patch('gcs_utils.authorization.authorize_with_rights')
    def test_get_snapshot(self, mock_authorize_with_rights, mock_get_settings, mock_conversation_db_v2_instance, client):
        mock_get_settings.return_value = Settings()
        mock_authorize_with_rights.return_value = {"user_id": "test_user", "rights": ["MANAGE_RAS_SEARCH_AI_CONVERSATION"]}

        user_id = "test_user"
        conversation_id = "conversation_007"
        snapshot_id = "snapshot_007"
        from conversation_core.shared.models.v2.conversation import Conversation, ConversationSnapshotResponse
        from conversation_core.shared.models.v3.conversation import ConversationV3
        snapshot = ConversationSnapshotResponse(
            user_id=user_id,
            snapshot_id=snapshot_id,
            conversation_id=conversation_id,
            conversation_entries=['conversation_entry_041'],
            ttl=0
        )
        conversation = ConversationV3(user_id=user_id, conversation_id=conversation_id, conversation_entries=[
            {
                'conversation_entry_id': 'conversation_entry_041',
                "status": "Complete",
                "user_input": "test_user1",
                "email_address": "<EMAIL>",
                "destination_url": "<EMAIL>",
                "conversation_entry_metadata": {},
                "conversation_action_type": "rag"
            }
        ])
        mock_conversation_db_v2_instance.get_conversation.return_value = conversation

        response = client.get(f"/api/v2/conversation/snapshot/{snapshot_id}", headers={
            "Authorization": "Bearer mocked_token"
        })

        assert response.status_code == 200
        json_data = response.json()
        print(json_data)
        assert json_data.get('conversation_id') == 'conversation_007'
        assert json_data.get('user_id') == 'test_user'
        assert len(json_data.get('conversation_entries')) == 1


if __name__ == "__main__":
    unittest.main()
