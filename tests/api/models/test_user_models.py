from unittest.mock import patch, PropertyMock
from fastapi import Request, HTTPException
from app.api.models.conversations.user_session_model import UserSession
from app.config.settings import Settings
import pytest


class TestUserSession:
    @pytest.fixture
    def mock_request(self):
        with patch.object(Request, 'headers', new_callable=PropertyMock) as mock_headers, \
                patch.object(Request, 'cookies', new_callable=PropertyMock) as mock_cookies, \
                patch.object(Request, 'url', new_callable=PropertyMock) as mock_url:
            yield mock_headers, mock_cookies, mock_url

    def test_user_session_with_valid_product_headers(self, mock_request):
        mock_headers, mock_cookies, mock_url = mock_request
        mock_headers.return_value = {
            'x-tr-sessionid': 'session_id',
            'x-tr-userid': 'user_guid',
            'x-tr-product-name': 'product_name',
            'x-tr-product-view': 'product_view',
            'x-tr-user-sensitivity': 'user_sensitivity',
        }
        mock_cookies.return_value = {}
        mock_url.return_value = 'http://test.com/v2'
        scope = {'type': 'http'}
        user_session = UserSession(Request(scope))
        assert user_session.get_session_info() is not None

    def test_user_session_with_valid_cobalt_headers(self, mock_request):
        mock_headers, mock_cookies, mock_url = mock_request
        mock_headers.return_value = {
            'x-cobalt-security-productview': 'product_view',
            'x-cobalt-security-productname': 'product_name',
            'x-cobalt-security-sessionid': 'session_id',
            'x-trmr-userguid': 'user_guid',
            'x-trmr-product': 'product_name',
            'x-cobalt-security-logprofile': 'log_profile',
        }
        mock_cookies.return_value = {}
        mock_url.return_value = 'http://test.com/v2'
        scope = {'type': 'http'}
        user_session = UserSession(Request(scope))
        assert user_session.get_session_info() is not None

    def test_user_session_with_invalid_product_name(self, mock_request):
        mock_headers, mock_cookies, mock_url = mock_request
        mock_headers.return_value = {}
        mock_cookies.return_value = {}
        mock_url.return_value = 'http://test.com/v2'
        scope = {'type': 'http'}
        user_session = None
        with pytest.raises(HTTPException) as exc_info:
            user_session = UserSession(Request(scope))
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == ('Product name (x-tr-product-name) not found in headers. This is a required '
                                         'header.')
        assert user_session is None or user_session.get_session_info() is None

    def test_user_session_with_invalid_product_view(self, mock_request):
        mock_headers, mock_cookies, mock_url = mock_request
        mock_headers.return_value = {}
        mock_cookies.return_value = {}
        mock_url.return_value = 'http://test.com/v2'
        scope = {'type': 'http'}
        user_session = None
        with pytest.raises(HTTPException) as exc_info:
            user_session = UserSession(Request(scope))
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == ('Product name (x-tr-product-name) not found in headers. This is a required '
                                         'header.')
        assert user_session is None or user_session.get_session_info() is None

    def test_user_session_with_v1_in_url(self, mock_request):
        mock_headers, mock_cookies, mock_url = mock_request
        mock_headers.return_value = {}
        mock_cookies.return_value = {}
        mock_url.return_value = 'http://test.com/v1'
        scope = {'type': 'http'}
        user_session = UserSession(Request(scope))
        assert user_session.get_session_info() is None
