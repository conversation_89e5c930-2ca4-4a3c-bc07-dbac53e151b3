import pytest
from unittest.mock import MagicMock, patch

import api
from fastapi.testclient import TestClient

mock_db = MagicMock()
mock_db_v2 = MagicMock()
mock_profile_service = MagicMock()


@pytest.fixture
def mock_answer_profile_service_instance():
    yield mock_profile_service
    mock_profile_service.reset_mock(return_value=True, side_effect=True)


@pytest.fixture
def mock_conversation_db_instance():
    yield mock_db
    mock_db.reset_mock(return_value=True, side_effect=True)


@pytest.fixture
def mock_conversation_db_v2_instance():
    yield mock_db_v2
    mock_db_v2.reset_mock(return_value=True, side_effect=True)


@pytest.fixture(scope='session', autouse=True)
def global_mocks():
    conversation_db_patcher = patch('conversation_core.shared.dynamo_helper.ConversationDB.__new__')
    conversation_db_v2_patcher = patch('conversation_core.shared.dynamo_helper_v2.ConversationDB.__new__')
    answer_profile_service_patcher = patch(
        'conversation_core.shared.services.profile_service.AnswerProfileService.__new__')

    mock_conversation_db = conversation_db_patcher.start()
    mock_conversation_db_v2 = conversation_db_v2_patcher.start()
    mock_answer_profile_service = answer_profile_service_patcher.start()

    mock_conversation_db.return_value = mock_db
    mock_conversation_db_v2.return_value = mock_db_v2
    mock_answer_profile_service.return_value = mock_profile_service

    yield {"db": mock_db, "db_v2": mock_db_v2, "profile_service": mock_profile_service}

    conversation_db_patcher.stop()
    conversation_db_v2_patcher.stop()
    answer_profile_service_patcher.stop()


async def mock_authorize_with_rights():
    return "Bearer mock_token"


@pytest.fixture
def client():
    from app.main import app
    app.dependency_overrides[api.auth.gcs_auth.authorize_with_rights] = mock_authorize_with_rights
    return TestClient(app)


@pytest.fixture
def mock_validate_if_previous_conv_in_progress():
    with patch("utils.validation_utils.validate_if_previous_conv_in_progress") as mock_function:
        yield mock_function


@pytest.fixture
def mock_validate_conv_lock():
    with patch("utils.validation_utils.validate_conv_lock") as mock_function:
        yield mock_function
