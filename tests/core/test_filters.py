import logging
import unittest

from app.core.filters import End<PERSON><PERSON><PERSON><PERSON>, DatadogStartupFilter


class TestEndpointFilter(unittest.TestCase):
    def test_filter_passes_on_info(self):
        endpoint_filter = EndpointFilter()
        record = logging.LogRecord(name='test_logger', level=logging.INFO, pathname='sample_pathname', lineno=0,
                                   msg='Request to api/endpoint', args=(), exc_info=None)
        result = endpoint_filter.filter(record)
        self.assertTrue(result)

    def test_filter_blocks_debug_with_health(self):
        endpoint_filter = EndpointFilter()
        record = logging.LogRecord(name='test_logger', level=logging.DEBUG, pathname='sample_pathname', lineno=0,
                                   msg='Request to /health endpoint', args=(), exc_info=None)
        result = endpoint_filter.filter(record)
        self.assertFalse(result)

    def test_filter_passes_on_error(self):
        endpoint_filter = EndpointFilter()
        record = logging.LogRecord(name='test_logger', level=logging.ERROR, pathname='sample_pathname', lineno=0,
                                   msg='This is an error message /health', args=(), exc_info=None)
        result = endpoint_filter.filter(record)
        self.assertFalse(result)


class TestDatadogStartupFilter(unittest.TestCase):
    def test_filter_warns_on_failed_startup(self):
        datadog_filter = DatadogStartupFilter()
        record = logging.LogRecord(name='test_logger', level=logging.ERROR, pathname='sample_pathname', lineno=0,
                                   msg='Failed to start collector MemoryCollector', args=(),
                                   exc_info=None)
        result = datadog_filter.filter(record)
        self.assertTrue(result)
        self.assertEqual(record.levelname, "WARN")

    def test_filter_passes_other_messages(self):
        datadog_filter = DatadogStartupFilter()
        record = logging.LogRecord(name='test_logger', level=logging.INFO, pathname='sample_pathname', lineno=0,
                                   msg='Successfully started application', args=(), exc_info=None)
        result = datadog_filter.filter(record)
        self.assertTrue(result)
        self.assertEqual(record.levelname, "INFO")
