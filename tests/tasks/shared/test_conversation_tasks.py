import time
import unittest
from unittest.mock import patch

from conversation_core.shared.enums import ConversationActionType

from app.tasks.shared.conversation_tasks import send_conversation_task, send_conversation_task_v2, evaluate_intent, \
    send_summarize_task


def assert_start_conversation_task_called_with(mock_obj, expected_args, time_sent):
    assert mock_obj.call_args is not None  # Ensure the method was called
    args, kwargs = mock_obj.call_args
    assert kwargs['args'] == expected_args
    headers = kwargs.get("headers", {})
    assert headers.get("green") is False
    assert headers.get("user_classification") == "unknown"


class TestConversationTasks:

    @patch("app.tasks.shared.conversation_tasks.CobaltConversationTask.start_conversation_task")
    def test_send_conversation_task(self, mock_start_conversation_task):
        # Define test data
        is_new_conversation = True
        user_id = '123'
        user_input = "How many cows in texas?"
        answer_solution_profile = "practical_chat_profile1"
        jurisdictions_override = ["MN-CS", "MN-CS-ALL"]
        content_types_override = ["CASE", "STATUTE", "REG<PERSON><PERSON><PERSON>ON", "KNOWHOW", "ANALYTICAL"]
        conversation_id = 'cov_156'
        conversation_entry_id = 'cov_entry_123'
        conversation_action_type = ConversationActionType.RAG
        auth_token = 'Bearer token'
        cobalt_session = None

        # Capture the time_sent value before calling the function
        time_sent = time.time()

        # Simulate a call to send_conversation_task
        send_conversation_task(
            is_new_conversation,
            user_id,
            user_input,
            answer_solution_profile,
            jurisdictions_override,
            content_types_override,
            conversation_id,
            conversation_entry_id,
            conversation_action_type,
            auth_token,
            cobalt_session,
            route_to_green=False,
            user_classification="unknown"
        )

        # Check if start_conversation_task was called with the correct arguments and ignore time_sent value
        expected_args = [
            is_new_conversation, user_id, user_input, answer_solution_profile,
            jurisdictions_override, content_types_override, conversation_id,
            conversation_entry_id, conversation_action_type, auth_token, cobalt_session,
        ]
        assert_start_conversation_task_called_with(mock_start_conversation_task.apply_async, expected_args, time_sent)

    @patch("app.tasks.shared.conversation_tasks.ConversationTaskV2.start_conversation_task")
    def test_send_conversation_task_v2(self, mock_start_conversation_task_v2):
        is_new_conversation = True
        user_id = 'user_145'
        user_input = "How many cows in texas?"
        answer_solution_profile = "practical_chat_profile1"
        jurisdictions_override = ["MN-CS", "MN-CS-ALL"]
        content_types_override = ["CASE", "STATUTE", "REGULATION", "KNOWHOW", "ANALYTICAL"]
        conversation_id = '45689'
        conversation_entry_id = '789456'
        conversation_action_type = ConversationActionType.RAG
        auth_token = 'Bearer_token'
        meta_data = {"green": "route_to_green", "user_classification": "unknown"}
        user_session = None

        time_sent = time.time()

        send_conversation_task_v2(is_new_conversation,
                                  user_id,
                                  user_input,
                                  answer_solution_profile,
                                  jurisdictions_override,
                                  content_types_override,
                                  conversation_id,
                                  conversation_entry_id,
                                  conversation_action_type,
                                  auth_token,
                                  meta_data,
                                  user_session=None
                                  )
        expected_args = [
            is_new_conversation, user_id, user_input, answer_solution_profile,
            jurisdictions_override, content_types_override, conversation_id,
            conversation_entry_id, conversation_action_type, auth_token, user_session, meta_data
        ]

        assert_start_conversation_task_called_with(mock_start_conversation_task_v2.apply_async, expected_args,
                                                   time_sent)

    @patch("app.tasks.shared.conversation_tasks.IntentResolverTask.evaluate_intent_task")
    def test_evaluate_intent(self, mock_intent_task):
        is_new_conversation = True
        user_id = 'evaluate_intent'
        user_input = "How are you?"
        answer_solution_profile = "practical_chat_profile1"
        jurisdictions_override = ["MN-CS", "MN-CS-ALL"]
        content_types_override = ["CASE", "STATUTE", "REGULATION", "KNOWHOW", "ANALYTICAL"]
        conversation_id = '45689'
        conversation_entry_id = '789456'
        conversation_action_type = ConversationActionType.RAG
        auth_token = 'Bearer_token'
        meta_data = {"green": "route_to_green", "user_classification": "unknown"}
        user_session = None
        time_sent = time.time()
        evaluate_intent(is_new_conversation,
                        user_id,
                        user_input,
                        answer_solution_profile,
                        jurisdictions_override,
                        content_types_override,
                        conversation_id,
                        conversation_entry_id,
                        conversation_action_type,
                        auth_token,
                        meta_data,
                        user_session=None
                        )
        expected_args = [
            is_new_conversation, user_id, user_input, answer_solution_profile,
            jurisdictions_override, content_types_override, conversation_id,
            conversation_entry_id, conversation_action_type, auth_token, user_session, meta_data
        ]

        assert_start_conversation_task_called_with(mock_intent_task.apply_async, expected_args, time_sent)

    @patch("app.tasks.shared.conversation_tasks.CobaltConversationTask.start_summarize_task")
    def test_send_summarize_task(self, mock_summarize_task):
        user_id = "task_1"
        legacy_id = "legacy_1"
        headnote_id = "headnote_1"
        citing_case_ids = "westlaw_keycite_profile1"
        answer_solution_profile = "practical_chat_profile1"
        conversation_id = "15689"
        conversation_entry_id = "entry_2"
        conversation_action_type = ConversationActionType.KEYCITE
        auth_token = "Bearer Token"
        cobalt_session = None

        # Capture the time_sent value before calling the function
        time_sent = time.time()
        send_summarize_task(user_id,
                            legacy_id,
                            headnote_id,
                            citing_case_ids,
                            answer_solution_profile,
                            conversation_id,
                            conversation_entry_id,
                            conversation_action_type,
                            auth_token,
                            cobalt_session,
                            route_to_green=False,
                            user_classification="unknown"
                            )
        expected_args = [
            user_id, legacy_id, headnote_id, answer_solution_profile, citing_case_ids, conversation_id,
            conversation_entry_id, conversation_action_type, auth_token, cobalt_session
        ]

        assert_start_conversation_task_called_with(mock_summarize_task.apply_async, expected_args, time_sent)