from unittest.mock import patch, Mock
import time

import pytest
from conversation_core.shared.models.v2.conversation_entry import ConversationEntryMetadata, ConversationEntry, \
    EmailNotification
from conversation_core.shared.utils.custom_exceptions import ProfileNotFoundException
from conversation_core.shared.models.v2.conversation import StartConversationRequest, ConversationMetadata, \
    CustomMetadata, Conversation
from conversation_core.shared.models.v3.conversation import StartConversationRequestV3
from conversation_core.shared.models.answer_profile import AnswerProfile
from pydantic_core._pydantic_core import ValidationError
from starlette import status
from starlette.exceptions import HTTPException
from conversation_core.shared.enums import RetrieveConversationEntryStatuses, ConversationActionType, AalpSkill
from conversation_core.shared.dynamo_helper_v2 import DynamoDBNotFoundException

from app.api.exceptions.conversation_exceptions import ConversationInProgressEntryException, \
    ConversationMaxHoursExceededException, InvalidConversationSkillException

test_user_input = "test_user_input"
test_answer_profile = "test_answer_profile"
test_user_id = "test_user_id"
test_conversation_id = "test_conversation_id"
test_conversation_entry_id = "test_conversation_entry_id"


class TestValidationUtils:
    @pytest.fixture
    def get_validation_utils(self):
        from app.utils import validation_utils
        return validation_utils

    @pytest.fixture
    def mock_validate_conversation_request_data(self):
        with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_profile:
            mock_conversation_entry_metadata = Mock(spec=ConversationEntryMetadata)
            mock_conversation_entry_metadata.email_notification = None
            mock_request = Mock(spec=StartConversationRequest)
            mock_request.answer_solution_profile = "test_profile"
            mock_request.content_types_override = ["text"]
            mock_request.conversation_entry_metadata = mock_conversation_entry_metadata
            mock_request.user_input = "test_input"
            mock_profile = Mock(spec=AnswerProfile)
            mock_profile.default_content_types = ["text"]
            mock_profile.supported_answer_content_types = ["text"]
            mock_get_profile.return_value = mock_profile
            yield mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata

    @pytest.fixture
    def mock_validate_conversation_skip_request_data(self):
        with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_profile_2:
            mock_conversation_entry_metadata = Mock(spec=ConversationEntryMetadata)
            mock_conversation_entry_metadata.email_notification = None
            mock_request = Mock(spec=StartConversationRequest)
            mock_request.answer_solution_profile = "test_profile"
            mock_request.content_types_override = ["text", "something_else"]
            mock_request.conversation_entry_metadata = mock_conversation_entry_metadata
            mock_request.user_input = "test_input"
            mock_profile = Mock(spec=AnswerProfile)
            mock_profile.default_content_types = ["text"]
            mock_profile.supported_answer_content_types = ["text"]
            mock_profile.bypass_content_type_check = True
            mock_get_profile_2.return_value = mock_profile
            yield mock_request, mock_profile, mock_get_profile_2, mock_conversation_entry_metadata

    def test_get_original_profile(self, get_validation_utils):
        validation_utils = get_validation_utils
        conversation_entries = [
            Mock(timestamp=123, result=Mock(answer_solution_profile="profile1")),
            Mock(timestamp=124, result=Mock(answer_solution_profile="profile2")),
            Mock(timestamp=125, result=Mock(answer_solution_profile="profile3"))
        ]
        result = validation_utils.get_original_profile(conversation_entries)
        assert result == "profile1"

    @pytest.mark.asyncio
    async def test_validate_conversation_request_content_error(self,
                                                               mock_validate_conversation_request_data,
                                                               get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data

        # Test when content_types_override is not a subset of default_content_types
        mock_profile.default_content_types = ["audio"]
        with pytest.raises(HTTPException) as exception:
            await validation_utils.validate_conversation_request(mock_request)
        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exception.value.detail == f"Content type is not allowed for {mock_request.answer_solution_profile}"

        # Test when content_types_exclude is not None and its length > 0
        mock_profile.default_content_types = ["text"]
        mock_request.content_types_exclude = ["audio"]
        with pytest.raises(HTTPException) as exception:
            await validation_utils.validate_conversation_request(mock_request)
        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exception.value.detail == "Requests cannot have both a content type override and exclude"

    @pytest.mark.asyncio
    async def test_validate_conversation_request_profile_error(self, mock_validate_conversation_request_data,
                                                               get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        # Test when ProfileNotFoundException is raised
        mock_get_profile.side_effect = ProfileNotFoundException
        with pytest.raises(HTTPException) as exception:
            await validation_utils.validate_conversation_request(mock_request)
        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    @patch("app.utils.validation_utils.validate_custom_field")
    async def test_validate_conversation_request(self, mock_validate_custom_field,
                                                 mock_validate_conversation_request_data, get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        # Test when everything is valid
        mock_get_profile.side_effect = None
        mock_request.content_types_exclude = None
        mock_profile.supported_answer_content_types = ["text"]
        await validation_utils.validate_conversation_request(mock_request)
        mock_validate_custom_field.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.utils.validation_utils.validate_custom_field")
    async def test_validate_conversation_request_skip(self, mock_validate_custom_field,
                                                      mock_validate_conversation_skip_request_data,
                                                      get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_skip_request_data
        # Test when everything is valid
        mock_get_profile.side_effect = None
        mock_request.content_types_exclude = None
        mock_profile.supported_answer_content_types = ["text"]
        await validation_utils.validate_conversation_request(mock_request)
        mock_validate_custom_field.assert_called_once()
        assert mock_profile.bypass_content_type_check is True

    @pytest.mark.asyncio
    async def test_validate_custom_field_with_valid_custom_fields(self, mock_validate_conversation_request_data,
                                                                  get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        mock_request.conversation_metadata = Mock(spec=ConversationMetadata)
        mock_request.conversation_metadata.custom = CustomMetadata(client_id="field1")
        mock_request.conversation_entry_metadata = Mock(spec=ConversationEntryMetadata)
        mock_request.conversation_entry_metadata.custom = CustomMetadata(client_id="field2")
        mock_profile.allowed_meta_data_fields = {"allowed_conv_fields": ["client_id"],
                                                 "allowed_conv_entry_field": ["client_id"]}

        result = await validation_utils.validate_custom_field(mock_request)

        assert result == mock_request

    @pytest.mark.asyncio
    async def test_validate_custom_field_with_invalid_conv_fields(self, mock_validate_conversation_request_data,
                                                                  get_validation_utils):
        validation_utils = get_validation_utils
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        mock_request.conversation_metadata = Mock(spec=ConversationMetadata)
        mock_request.conversation_metadata.custom = CustomMetadata(client_id="field1")
        mock_request.conversation_entry_metadata = Mock(spec=ConversationEntryMetadata)
        mock_request.conversation_entry_metadata.custom = CustomMetadata(client_id="field2")
        mock_profile.allowed_meta_data_fields = {"allowed_conv_entry_field": ["client_id"]}

        # Scenario 1: Invalid conversation custom metadata field
        with pytest.raises(HTTPException) as exception:
            await validation_utils.validate_custom_field(mock_request)

        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exception.value.detail == f"Custom field is not allowed for {mock_request.answer_solution_profile}"

        mock_profile.allowed_meta_data_fields = {"allowed_conv_fields": ["field1"]}

        # Scenario 2: Invalid conversation entry custom metadata field
        with pytest.raises(HTTPException) as exception:
            await validation_utils.validate_custom_field(mock_request)

        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exception.value.detail == f"Custom field is not allowed for {mock_request.answer_solution_profile}"

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_metadata_with_valid_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_metadata = Mock(spec=ConversationMetadata)
        conversation_metadata.custom = CustomMetadata(client_id="field1")
        with patch("app.utils.validation_utils.get_answer_solution_profile") as mock_get_profile:
            mock_get_profile.return_value = "test_profile"
            with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_answer_profile:
                mock_answer_profile = Mock(spec=AnswerProfile)
                mock_answer_profile.allowed_meta_data_fields = {"allowed_conv_fields": ["client_id"]}
                mock_get_answer_profile.return_value = mock_answer_profile
                result = await validation_utils.validate_custom_field_in_metadata(user_id, conversation_id,
                                                                                  conversation_metadata)
                assert result == conversation_metadata

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_metadata_with_invalid_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_metadata = Mock(spec=ConversationMetadata)
        conversation_metadata.custom = CustomMetadata(client_id="field1")
        with patch("app.utils.validation_utils.get_answer_solution_profile") as mock_get_profile:
            mock_get_profile.return_value = "test_profile"
            with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_answer_profile:
                mock_answer_profile = Mock(spec=AnswerProfile)
                mock_answer_profile.allowed_meta_data_fields = {"allowed_conv_fields": ["field2"]}
                mock_get_answer_profile.return_value = mock_answer_profile
                with pytest.raises(HTTPException) as exception:
                    await validation_utils.validate_custom_field_in_metadata(user_id, conversation_id,
                                                                             conversation_metadata)
                assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
                assert exception.value.detail == "Custom field is not allowed for test_profile"

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_metadata_with_no_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_metadata = Mock(spec=ConversationMetadata)
        conversation_metadata.custom = None
        result = await validation_utils.validate_custom_field_in_metadata(user_id, conversation_id,
                                                                          conversation_metadata)
        assert result == conversation_metadata

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_entry_metadata_with_valid_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_entry_id = "entry_345"
        conversation_entry_meta_data = Mock(spec=ConversationEntryMetadata)
        conversation_entry_meta_data.custom = CustomMetadata(client_id="field1")
        with patch("app.utils.validation_utils.get_answer_solution_profile") as mock_get_profile:
            mock_get_profile.return_value = "test_profile"
            with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_answer_profile:
                mock_answer_profile = Mock(spec=AnswerProfile)
                mock_answer_profile.allowed_meta_data_fields = {"allowed_conv_entry_field": ["client_id"]}
                mock_get_answer_profile.return_value = mock_answer_profile
                result = await validation_utils.validate_custom_field_in_entry_metadata(user_id, conversation_id,
                                                                                        conversation_entry_id,
                                                                                        conversation_entry_meta_data)
                assert result == conversation_entry_meta_data

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_entry_metadata_with_invalid_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_entry_id = "entry_345"
        conversation_entry_meta_data = Mock(spec=ConversationEntryMetadata)
        conversation_entry_meta_data.custom = CustomMetadata(client_id="field1")
        with patch("app.utils.validation_utils.get_answer_solution_profile") as mock_get_profile:
            mock_get_profile.return_value = "test_profile"
            with patch("app.utils.validation_utils.answer_profile_service.get_profile") as mock_get_answer_profile:
                mock_answer_profile = Mock(spec=AnswerProfile)
                mock_answer_profile.allowed_meta_data_fields = {"allowed_conv_entry_field": ["field2"]}
                mock_get_answer_profile.return_value = mock_answer_profile
                with pytest.raises(HTTPException) as exception:
                    await validation_utils.validate_custom_field_in_entry_metadata(user_id, conversation_id,
                                                                                   conversation_entry_id,
                                                                                   conversation_entry_meta_data)
                assert exception.value.status_code == status.HTTP_400_BAD_REQUEST
                assert exception.value.detail == "Custom field is not allowed for test_profile"

    @pytest.mark.asyncio
    async def test_validate_custom_field_in_entry_metadata_with_no_custom_fields(self, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_entry_id = "entry_345"
        conversation_entry_meta_data = Mock(spec=ConversationEntryMetadata)
        conversation_entry_meta_data.custom = None
        result = await validation_utils.validate_custom_field_in_entry_metadata(user_id, conversation_id,
                                                                                conversation_entry_id,
                                                                                conversation_entry_meta_data)
        assert result == conversation_entry_meta_data

    @pytest.mark.asyncio
    async def test_log_and_raise_error(self, get_validation_utils):
        validation_utils = get_validation_utils
        with pytest.raises(HTTPException) as exception:
            await validation_utils.log_and_raise_error(status_code=status.HTTP_400_BAD_REQUEST,
                                                       message="Input is invalid")
        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_get_answer_solution_profile(self, mock_conversation_db_v2_instance, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        mock_conversation_db_v2_instance.retrieve_item.return_value = {
            'profile': 'westlaw_chat_profile2'
        }

        result = await validation_utils.get_answer_solution_profile(user_id, conversation_id)
        assert result == "westlaw_chat_profile2"

    @pytest.mark.asyncio
    async def test_get_answer_solution_profile_profile_exception(self, mock_conversation_db_v2_instance,
                                                                 get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        mock_conversation_db_v2_instance.retrieve_item.return_value = {
            'profile': None
        }

        with pytest.raises(HTTPException) as exception:
            await validation_utils.get_answer_solution_profile(user_id, conversation_id)
        assert exception.value.status_code == status.HTTP_404_NOT_FOUND
        assert exception.value.detail == "For the provided conversation id, the profile information doesn't exist."

        mock_conversation_db_v2_instance.retrieve_item.return_value = {
            'Item': {'profile': 'westlaw_chat_profile2'}
        }
        with pytest.raises(HTTPException) as exception:
            await validation_utils.log_and_raise_error(status_code=status.HTTP_404_NOT_FOUND,
                                                       message="Input is invalid")
        assert exception.value.status_code == status.HTTP_404_NOT_FOUND

        mock_conversation_db_v2_instance.retrieve_item.side_effect = IndexError("Test IndexError")
        with pytest.raises(HTTPException) as exception:
            await validation_utils.get_answer_solution_profile(user_id, conversation_id)
        assert exception.value.status_code == status.HTTP_400_BAD_REQUEST

        mock_conversation_db_v2_instance.retrieve_item.side_effect = DynamoDBNotFoundException(user_id, conversation_id)
        with pytest.raises(HTTPException) as exception:
            await validation_utils.get_answer_solution_profile(user_id, conversation_id)
        assert exception.value.status_code == status.HTTP_404_NOT_FOUND

        mock_conversation_db_v2_instance.retrieve_item.side_effect = Exception("Test Exception")
        with pytest.raises(Exception) as exception:
            await validation_utils.get_answer_solution_profile(user_id, conversation_id)
        assert exception.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @pytest.mark.asyncio
    @patch("app.utils.validation_utils.get_answer_solution_profile")
    async def test_validate_custom_field_in_entry_metadata(self, mock_profile, get_validation_utils):
        validation_utils = get_validation_utils
        user_id = "user123"
        conversation_id = "conv123"
        conversation_entry_id = "entry_345"
        v2_result = ConversationEntryMetadata(email_notification={
            "email_address": "<EMAIL>",
            "send_email": False,
            "destination_url": "www.google.com",
            "email_sent": False
        })
        mock_profile.return_value = 'westlaw_chat_profile2'
        result = await validation_utils.validate_custom_field_in_entry_metadata(user_id, conversation_id,
                                                                                conversation_entry_id,
                                                                                conversation_entry_metadata=v2_result)
        assert result is not None

    def test_validate_if_previous_conv_in_progress(self, get_validation_utils):
        conversation = Mock()
        conversation.conversation_entries = [
            Mock(conversation_action_type=ConversationActionType.RAG,
                 status=RetrieveConversationEntryStatuses.IN_PROGRESS, timestamp=time.time() - 300)
        ]
        with pytest.raises(ConversationInProgressEntryException) as exception:
            get_validation_utils.validate_if_previous_conv_in_progress(conversation)
        assert exception.value.status_code == 433

    def test_validate_if_previous_conv_in_progress_no_entry(self, get_validation_utils):
        validation_utils = get_validation_utils
        conversation = Mock()
        conversation.conversation_entries = []
        # No exception should be raised in this case
        validation_utils.validate_if_previous_conv_in_progress(conversation)

    def test_validate_conversation_for_followup(self, get_validation_utils):
        conversation = Mock()
        conversation.conversation_metadata.max_followup_date = time.time() - 7200  # 2 hours ago
        profile = AnswerProfile(max_entries_per_conversation=-1)
        validation_utils = get_validation_utils

        with pytest.raises(ConversationMaxHoursExceededException) as exception:
            validation_utils.validate_conversation_for_followup(conversation, profile)
        assert exception.value.status_code == 434

    def test_validate_conv_lock_no_expiry(self, get_validation_utils):
        conversation = Mock()
        conversation.conversation_metadata.max_followup_date = time.time() - 3600  # 1 hour ago
        profile = AnswerProfile(max_entries_per_conversation=-1)
        validation_utils = get_validation_utils
        with pytest.raises(ConversationMaxHoursExceededException) as exception:
            validation_utils.validate_conversation_for_followup(conversation, profile)
        assert exception.value.status_code == 434

    def test_validate_conversation_for_followup_v3_complete_conversation_entry(self, get_validation_utils):
        validation_utils = get_validation_utils
        conversation_metadata = ConversationMetadata(latest_entry_status=RetrieveConversationEntryStatuses.COMPLETE)
        conversation_entry = ConversationEntry(conversation_entry_id=test_conversation_entry_id,
                                               conversation_action_type=ConversationActionType.SKILL_ROUTER,
                                               status=RetrieveConversationEntryStatuses.COMPLETE,
                                               timestamp=round(time.time() - 300))
        conversation = Conversation(user_id=test_user_id,
                                    conversation_id=test_conversation_id,
                                    conversation_metadata=conversation_metadata,
                                    conversation_entries=[conversation_entry])
        # No exception should be raised in this case
        validation_utils.validate_if_previous_conv_in_progress(conversation=conversation,
                                                               from_v3_endpoint=True)

    def test_validate_conversation_for_followup_v3_in_progress_conversation_entry(self, get_validation_utils):
        conversation_metadata = ConversationMetadata(latest_entry_status=RetrieveConversationEntryStatuses.IN_PROGRESS)
        conversation_entry = ConversationEntry(conversation_entry_id=test_conversation_entry_id,
                                               conversation_action_type=ConversationActionType.SKILL_ROUTER,
                                               status=RetrieveConversationEntryStatuses.IN_PROGRESS,
                                               timestamp=round(time.time() - 300))
        conversation = Conversation(user_id=test_user_id,
                                    conversation_id=test_conversation_id,
                                    conversation_metadata=conversation_metadata,
                                    conversation_entries=[conversation_entry])
        with pytest.raises(ConversationInProgressEntryException) as exception:
            get_validation_utils.validate_if_previous_conv_in_progress(conversation=conversation,
                                                                       from_v3_endpoint=True)
        assert exception.value.status_code == 433

    def test_validate_conversation_for_followup_v3_in_progress_conversation_with_complete_entries(self,
                                                                                                  get_validation_utils):
        conversation_metadata = ConversationMetadata(latest_entry_status=RetrieveConversationEntryStatuses.IN_PROGRESS,
                                                     latest_entry_date=round(time.time() - 300))
        conversation_entry = ConversationEntry(conversation_entry_id=test_conversation_entry_id,
                                               conversation_action_type=ConversationActionType.SKILL_ROUTER,
                                               status=RetrieveConversationEntryStatuses.COMPLETE,
                                               timestamp=round(time.time() - 300))
        conversation = Conversation(user_id=test_user_id,
                                    conversation_id=test_conversation_id,
                                    conversation_metadata=conversation_metadata,
                                    conversation_entries=[conversation_entry])
        with pytest.raises(ConversationInProgressEntryException) as exception:
            get_validation_utils.validate_if_previous_conv_in_progress(conversation=conversation,
                                                                       from_v3_endpoint=True)
        assert exception.value.status_code == 433

    @pytest.mark.asyncio
    async def test_validate_conversation_request_for_v3_no_subscribed_skills(self, get_validation_utils):
        conversation_request = StartConversationRequestV3(user_input=test_user_input,
                                                          answer_solution_profile=test_answer_profile)
        with pytest.raises(InvalidConversationSkillException) as exception:
            await get_validation_utils.validate_conversation_v3_request(
                start_conversation_request=conversation_request)

    @pytest.mark.asyncio
    async def test_validate_conversation_request_for_v3_empty_subscribed_skills(self, get_validation_utils):
        conversation_request = StartConversationRequestV3(user_input=test_user_input,
                                                          answer_solution_profile=test_answer_profile,
                                                          subscribed_skills=[])
        validated_conversation_request = await get_validation_utils.validate_conversation_v3_request(
            start_conversation_request=conversation_request)
        assert validated_conversation_request.user_input == test_user_input

    def test_validate_conversation_request_model_for_v3_invalid_skill(self, get_validation_utils):
        with pytest.raises(ValidationError) as exception:
            conversation_request = StartConversationRequestV3(user_input=test_user_input,
                                                              answer_solution_profile=test_answer_profile,
                                                              subscribed_skills=["invalid_skill"])

    @pytest.mark.asyncio
    async def test_validate_conversation_request_for_v3_happy_path(self, get_validation_utils):
        conversation_request = StartConversationRequestV3(user_input=test_user_input,
                                                          answer_solution_profile=test_answer_profile,
                                                          subscribed_skills=[AalpSkill.WESTLAW_US,
                                                                             AalpSkill.FIFTY_STATE_SURVEY,
                                                                             AalpSkill.CLAIMS_EXPLORER])
        validated_conversation_request = await get_validation_utils.validate_conversation_v3_request(
            start_conversation_request=conversation_request
        )

        assert validated_conversation_request.user_input == test_user_input

    @pytest.mark.asyncio
    async def test_check_header_validity_success(self,get_validation_utils):
        test_header = "test"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
        test_header = "test123"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
        test_header = "test-123"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
        test_header = "test_123"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
        test_header = "test.123"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
        test_header = "test 123"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated == test_header
    @pytest.mark.asyncio
    async def test_check_header_validity_null(self,get_validation_utils):
        test_header = "null"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated is None
        test_header = "Null"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated is None
        test_header = "None"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated is test_header
        test_header = "NONE"
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated is test_header
        test_header = None
        test_header_validated = get_validation_utils.check_header_validity(test_header,"test_header")
        assert test_header_validated is None

    @pytest.mark.asyncio
    async def test_check_header_validity_exception(self,get_validation_utils):
        test_header = "test!?$"
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.check_header_validity(test_header,"test_header")

    @pytest.mark.asyncio
    async def test_email_validation_success(self,get_validation_utils,mock_validate_conversation_request_data):
        emailNotification = EmailNotification(
            email_address = "<EMAIL>",
            send_email = True,
            destination_url = "url.com")
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        mock_conversation_entry_metadata.email_notification = emailNotification
        test = get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        assert emailNotification == mock_conversation_entry_metadata.email_notification

    @pytest.mark.asyncio
    async def test_email_validation_error_email_address(self,get_validation_utils,mock_validate_conversation_request_data):
        email_notification = EmailNotification(
            email_address = "",
            send_email = True,
            destination_url = "url.com")
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        mock_conversation_entry_metadata.email_notification = email_notification
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.email_address = None
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.email_address = "none"
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.email_address = "null"
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)

    @pytest.mark.asyncio
    async def test_email_validation_error_url(self,get_validation_utils,mock_validate_conversation_request_data):
        email_notification = EmailNotification(
            email_address = "<EMAIL>",
            send_email = True,
            destination_url = "")
        mock_request, mock_profile, mock_get_profile, mock_conversation_entry_metadata = mock_validate_conversation_request_data
        mock_conversation_entry_metadata.email_notification = email_notification
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.destination_url = None
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.destination_url = "none"
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)
        mock_conversation_entry_metadata.email_notification.destination_url = "null"
        with pytest.raises(HTTPException) as exception:
            await get_validation_utils.validate_conversation_entry_metadata(mock_conversation_entry_metadata)