import pytest

from app.utils.string_utils import snake_to_camel_case


def test_snake_to_camel_case_with_string():
    result = snake_to_camel_case("strings")
    assert result == "strings"


def test_snake_to_camel_case_empty_string():
    result = snake_to_camel_case("")
    assert result == ""


def test_snake_to_camel_case_with_one_word():
    result = snake_to_camel_case("camelcase")
    assert result == "camelcase"


def test_snake_to_camel_case_with_multiple_underscores():
    result = snake_to_camel_case("multi__under___score")
    assert result == "multiUnderScore"


def test_snake_to_camel_case_with_none():
    with pytest.raises(TypeError):
        snake_to_camel_case(None)
